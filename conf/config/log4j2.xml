<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="off" monitorInterval="1800">

    <Appenders>
        <!-- 异常日志 -->
        <RollingRandomAccessFile name="errorLog"
                                 fileName="${log.root}/logs/common-error.log"
                                 filePattern="${log.root}/logs/common-error.log.%d{yyyy-MM-dd}-%i.gz">
            <PatternLayout charset="UTF-8"
                           pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p [%X{LOGGER_MODULE}][%X{LOGGER_CATEGORY}][%X{LOGGER_SUBCATEGORY}][%X{LOGGER_FILTER1}][%X{LOGGER_FILTER2}]%X{apmTrace}%m%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="256 MB"/>
            </Policies>

            <DefaultRolloverStrategy max="10"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>

        <!-- 正常日志 -->
        <RollingRandomAccessFile name="normalLog"
                                 fileName="${log.root}/logs/shared-mobility-asr-admin.log"
                                 filePattern="${log.root}/logs/shared-mobility-asr-admin.log.%d{yyyy-MM-dd}-%i.gz">
            <PatternLayout charset="UTF-8"
                           pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p [%X{LOGGER_MODULE}][%X{LOGGER_CATEGORY}][%X{LOGGER_SUBCATEGORY}][%X{LOGGER_FILTER1}][%X{LOGGER_FILTER2}]%X{apmTrace}%m%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="256 MB"/>
            </Policies>

            <DefaultRolloverStrategy max="10"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="ACCEPT"/>
            </Filters>
        </RollingRandomAccessFile>
        <!-- JVM日志 -->
        <RollingRandomAccessFile name="JVM-MONITOR-DIGEST-APPENDER"
                                 fileName="${log.root}/logs/jvm-monitor-digest.log"
                                 filePattern="${log.root}/logs/jvm-monitor-digest.%d{yyyy-MM-dd}-%i.gz">
            <PatternLayout charset="UTF-8"
                           pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p [%X{LOGGER_MODULE}][%X{LOGGER_CATEGORY}][%X{LOGGER_SUBCATEGORY}][%X{LOGGER_FILTER1}][%X{LOGGER_FILTER2}]%X{apmTrace}%m%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="256 MB"/>
            </Policies>

            <DefaultRolloverStrategy max="10"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="ACCEPT"/>
            </Filters>
        </RollingRandomAccessFile>

    </Appenders>

    <Loggers>
        <!-- 应用业务日志 -->
        <logger name="com.ly.travel.shared.mobility" additivity="false" level="info" includeLocation="false">
            <appender-ref ref="errorLog"/>
            <appender-ref ref="normalLog"/>
        </logger>

        <!-- sof日志 -->
        <logger name="com.ly.sof" level="info"/>
        <logger name="com.ly.sof.api.i18n" level="warn"/>
        <logger name="com.ly.sof.api.mq.consumer.DefaultUniformEventSubscriber" level="warn"/>
        <logger name="JVM-MONITOR-DIGEST-LOGGER" additivity="false" level="info">
            <appender-ref ref="JVM-MONITOR-DIGEST-APPENDER"/>
        </logger>
        <!-- 框架日志 -->
        <logger name="com.ly.dsf" level="warn"/>
        <logger name="com.ly.spat.dsf" level="warn"/>
        <logger name="org.springframework" level="warn"/>
        <logger name="com.ly.tcbase.core.log.ComponentLog" level="warn"/>
        <logger name="com.alibaba.dubbo"/>
        <logger name="com.alibaba.dubbo.rpc.protocol.rest.support" level="warn"/>
        <logger name="com.alibaba.dubbo.common.serialize.support.kryo.CompatibleKryo" level="error"/>
        <logger name="com.alibaba.dubbo.registry.tcdsf.ControlCenterClient" level="warn"/>
        <logger name="com.alibaba.dubbo.registry.tcdsf.TcDsfRegistry" level="warn"/>
        <logger name="com.alibaba.dubbo.common.logger.LoggerFactory"/>
        <logger name="RocketmqClient" level="error"/>
        <logger name="DefaultMQPushConsumerImpl" level="error"/>

        <logger name="io.lettuce.core" level="error"/>

        <root level="info" includeLocation="false">
            <appender-ref ref="errorLog"/>
            <appender-ref ref="normalLog"/>
        </root>
    </Loggers>
</Configuration>
