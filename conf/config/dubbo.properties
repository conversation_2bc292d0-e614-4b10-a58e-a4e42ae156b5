sof.version=${sof.version}
app.name=${app.name}
app.version=${app.version}
app.type=${app.type}
app.root=${app.root}
sof-env=${sof-env}

#db
uniform.env=${uniform.env}
uniform.skyCode=${uniform.skyCode}
uniform.dbName=${uniform.dbName}

#dubbo
dubbo.application.name=${dubbo.application.name}
dubbo.registry.address=${dubbo.registry.address}
dubbo.container=${dubbo.container}
dubbo.port=${dubbo.port}
dubbo.service.deploy.container=${dubbo.service.deploy.container}
dubbo.tcdsfGroup.gsName=${dubbo.tcdsfGroup.gsName}
dubbo.tcdsfGroup.version=${dubbo.tcdsfGroup.version}

#turbomq\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709mq\uFF0C\u53EF\u4EE5\u5220\u9664
mq.nameSrvAddress=${mq.nameSrvAddress}

#drm\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709drm\uFF0C\u53EF\u4EE5\u5220\u9664
conf.domain=${conf.domain}
zkconnect=${zkconnect}
rootPath=${rootPath}

#redis\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709redis\uFF0C\u53EF\u4EE5\u5220\u9664
redis.groupName=${redis.groupName}

#kafka\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709kafka\uFF0C\u53EF\u4EE5\u5220\u9664
kfk.projectName=${kfk.projectName}
kfk.projectCode=${kfk.projectCode}

#\u77ED\u4FE1\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709\u77ED\u4FE1\u53D1\u9001\uFF0C\u53EF\u4EE5\u5220\u9664
mq.goto.message.topic=${mq.goto.message.topic}
mq.goto.message.code=${mq.goto.message.code}

#sso\uFF0C\u9879\u76EE\u975E\u7F51\u9875\u5E94\u7528\u53EF\u4EE5\u5220\u9664
sec.interface.url=${sec.interface.url}
sec.login.url=${sec.login.url}
sec.loginout.url=${sec.loginout.url}
sec.index.url=${sec.index.url}
sec.interface.projectCode=${sec.interface.projectCode}
sec.sso.url=${sec.sso.url}
sec.currentSys.url=${sec.currentSys.url}
session.timeout=${session.timeout}
sec.redis.version=V1
#sec.redis.groupName\u81EA\u5B9A\u4E49redis\u7EC4\u540D\uFF0C\u9ED8\u8BA4\u4E0EappName\u76F8\u540C\u3002

#http client
http_read_timeout=${http_read_timeout}
connect_timeout=${connect_timeout}

#js\u90E8\u7F72
#\u914D\u7F6E\u6587\u4EF6
toolkit-accessKey=XFR136ED5J4FFAY6AY3T
toolkit-accessSecretKey=G651i643mTQ0uEXLM9lEzp3GTEooBBvSgFSlglWB
toolkit-bucket=iflight-toolkit
toolkit-endPoint=tcstore1.17usoft.com
toolkit.redis.group=flight.java.buddha.mng


mq.risk.core.test.group=risk_core_test_group
mq.risk.core.test.topic=risk_core_test_topic

# crm client \u76D1\u542Cmq\u7684\u6D88\u8D39\u7EC4
travel.mobility.supply.crm.client.mq.group=travel_risk_core_search_crm_group
# \u9700\u8981\u52A0\u8F7D\u7684crm\u6A21\u5757\u53EF\u53C2\u8003 com.ly.travel.shared.mobility.supply.crm.core.model.enums.CrmClientDataModuleEnum
travel.mobility.supply.crm.client.modules=supplierBaseConfig,basicDataCity
travel.mobility.supply.crm.core.dsf.version=1.0.0.0
travel.mobility.supply.crm.core.dsf.gsName=${travel.mobility.supply.crm.core.dsf.gsName}

car.mng.url=${car.mng.url}

mq.risk.core.handle.topic=mq_risk_core_handle_topic

dsf.car.shared.mobility.order.service.gsName=${dsf.car.shared.mobility.order.service.gsName}
dsf.car.shared.mobility.order.service.version=${dsf.car.shared.mobility.order.service.version}

dsf.car.shared.mobility.order.core.gsName=${dsf.car.shared.mobility.order.core.gsName}
dsf.car.shared.mobility.order.core.version=${dsf.car.shared.mobility.order.core.version}

labrador.shared.car.order.url=${labrador.shared.car.order.url}
labrador.shared.car.order.token=${labrador.shared.car.order.token}

approve.apply.detail.url=${approve.apply.detail.url}
marketing.mng2.url=${marketing.mng2.url}

#????????
funasr.server.url=${funasr.server.url}
#???????????
upload.file.temp.path=${upload.file.temp.path}


# car.order.service
car.order.service.gsName=${car.order.service.gsName}
car.order.service.version=${car.order.service.version}
