package com.ly.travel.shared.mobility.integration.client.ordermng;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ly.travel.shared.mobility.integration.client.ordermng.req.QueryWorkOrderReq;
import com.ly.travel.shared.mobility.integration.client.ordermng.resp.OrderMngBaseResp;
import com.ly.travel.shared.mobility.integration.client.ordermng.resp.WorkOrderQueryResp;
import com.ly.travel.shared.mobility.integration.utils.OkHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
@Slf4j
public class OrderMngClient {
    @Value("${car.mng.url}")
    private String carMngUrl;



    /**
     * 查询工单信息
     *
     * @param workId
     * @return
     */
    public WorkOrderQueryResp queryWorkOrder(String workId) {
        QueryWorkOrderReq req = new QueryWorkOrderReq();
        req.setOrderSerialNo(workId);
        OrderMngBaseResp<WorkOrderQueryResp> resp = doPost(req, new TypeReference<OrderMngBaseResp<WorkOrderQueryResp>>() {
        }, "queryWorkOrderDetail", 3000);
        if (isSuccess(resp)) {
            return resp.getData();
        }
        return null;
    }

    private boolean isSuccess(OrderMngBaseResp resp) {
        return resp != null && resp.isSuccess();
    }

    public <Res extends OrderMngBaseResp, Req> Res doPost(Req request, TypeReference<Res> reference, String subPath, int timeout) {
        String reqUrl = carMngUrl + subPath;
        String reqJson = JSON.toJSONString(request);
        log.info("调用{}接口请求,url:{} reqJson:{}", subPath, reqUrl, reqJson);
        String postJsonStr = null;
        try {
            postJsonStr = OkHttpUtils.postJson(reqUrl, reqJson, timeout);
        } catch (IOException e) {
            log.error("调用{}接口失败", subPath, e);
        }
        log.info("调用{}接口返回, resp:{}", subPath, postJsonStr);
        return JSON.parseObject(postJsonStr, reference);
    }
}
