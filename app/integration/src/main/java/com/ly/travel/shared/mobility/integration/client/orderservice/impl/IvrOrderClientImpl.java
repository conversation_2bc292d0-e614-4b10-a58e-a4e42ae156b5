package com.ly.travel.shared.mobility.integration.client.orderservice.impl;

import com.ly.travel.car.orderservice.facade.IVRCarOrderFacade;
import com.ly.travel.car.orderservice.facade.request.ivr.IVROrderDetailRequest;
import com.ly.travel.car.orderservice.facade.request.ivr.IVROrderListRequest;
import com.ly.travel.car.orderservice.facade.response.ivr.IVROrderDetailResponse;
import com.ly.travel.car.orderservice.facade.response.ivr.IVROrderListResponse;
import com.ly.travel.shared.mobility.integration.client.orderservice.IvrOrderClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: 订单查询
 * @Author: jay.he
 * @Date: 2025-08-19 11:31
 * @Version: 1.0
 **/
@Slf4j
@Service
public class IvrOrderClientImpl implements IvrOrderClient {

    @Resource
    private IVRCarOrderFacade carIvrOrderFacade;

    @Override
    public IVROrderListResponse queryIvrOrderList(IVROrderListRequest request) {
        return carIvrOrderFacade.orderList(request);
    }

    @Override
    public IVROrderDetailResponse orderDetail(IVROrderDetailRequest request) {
        return carIvrOrderFacade.orderDetail(request);
    }
}
