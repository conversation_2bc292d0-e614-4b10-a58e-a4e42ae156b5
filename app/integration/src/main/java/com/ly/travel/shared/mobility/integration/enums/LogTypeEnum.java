package com.ly.travel.shared.mobility.integration.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;


@Getter
@AllArgsConstructor
public enum LogTypeEnum {

    RISK_ORDER("风险单"),
    RISK_SCENE("风控节点"),
    RISK_CUSTOMER("风控名单"),
    OFFLINE_METRIC_FIELD("离线指标"),
    OFFLINE_METRIC_STRATEGY("离线策略"),
    SENSITIVE_WORDS("敏感词"),

    ONLINE_METRIC_SCENE("在线场景"),
    ONLINE_METRIC_FIELD("在线指标"),
    ONLINE_METRIC_RULE("在线规则"),
    ONLINE_METRIC_STRATEGY("在线策略"),
    ;

    /**
     * The constant MAPPING.
     */
    public static final Map<String, LogTypeEnum> MAPPING = Maps.newHashMap();

    static {
        for (LogTypeEnum value : LogTypeEnum.values()) {
            MAPPING.put(value.name(), value);
        }
    }

    /**
     * 枚举描述
     */
    private final String desc;

    /**
     * 根据枚举编码获取枚举对象
     *
     * @param code 枚举编码
     * @return 枚举对象 enum by code
     */
    public static LogTypeEnum getEnumByCode(String code) {
        return MAPPING.get(code);
    }

    public static String getDescByCode(String code) {
        LogTypeEnum typeEnum = MAPPING.get(code);
        if(typeEnum == null){
            return StringUtils.EMPTY;
        }
        return typeEnum.getDesc();
    }
}
