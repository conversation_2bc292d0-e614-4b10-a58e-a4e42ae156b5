package com.ly.travel.shared.mobility.integration.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;


@Getter
@AllArgsConstructor
public enum SupplierRiskStatusEnum {
    NOT_SEND(0, "未发送"),
    HAD_SEND(1, "已发送"),
    APPEALING(2, "申诉中"),
    DRIVER_FAULT(3, "司机有责"),
    DRIVER_NO_FAULT(4, "司机无责"),
    ;

    private Integer code;
    private String desc;

    public static String getDescByCode(Integer code){
        SupplierRiskStatusEnum typeEnum = Arrays.stream(values()).filter(s -> s.getCode().equals(code)).findFirst().orElse(null);
        if(typeEnum !=null){
            return typeEnum.desc;
        }
        return StringUtils.EMPTY;
    }
}
