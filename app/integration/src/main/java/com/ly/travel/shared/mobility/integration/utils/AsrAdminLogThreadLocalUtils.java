package com.ly.travel.shared.mobility.integration.utils;

import com.ly.travel.shared.mobility.integration.utils.dto.LogAspectTempDTO;

public class AsrAdminLogThreadLocalUtils {
    private static final ThreadLocal<LogAspectTempDTO> threadLocal = new ThreadLocal<>();
    private static AsrAdminLogThreadLocalUtils instance;

    private AsrAdminLogThreadLocalUtils() {} // 私有构造函数，防止外部实例化

    public static AsrAdminLogThreadLocalUtils getInstance() {
        if (instance == null) {
            synchronized (AsrAdminLogThreadLocalUtils.class) {
                // 第二次检查，在同步块内再次检查，确保只有一个线程创建实例
                if (instance == null) {
                    instance = new AsrAdminLogThreadLocalUtils();
                }
            }
        }
        return instance;
    }

    public void setValue(LogAspectTempDTO value) {
        threadLocal.set(value);
    }

    public LogAspectTempDTO getValue() {
        return threadLocal.get();
    }

    public void removeValue() {
        threadLocal.remove();
    }
}
