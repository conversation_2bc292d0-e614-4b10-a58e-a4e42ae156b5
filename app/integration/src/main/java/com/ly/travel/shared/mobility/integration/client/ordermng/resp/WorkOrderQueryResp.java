package com.ly.travel.shared.mobility.integration.client.ordermng.resp;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class WorkOrderQueryResp {

    /**
     * 工单流水
     */
    private String orderSerialNo;
    /**
     * 外部流水号
     */
    private String extSerialNo;
    /**
     *
     */
    private String domainKey;
    /**
     *
     */
    private String busiSceneKey;
    /**
     * 类别
     */
    private String sceneCode;
    /**
     * 类型
     */
    private String typeCode;
    /**
     * 处理人组
     */
    private String createrNumber;
    /**
     * 工号
     */
    private String createrName;
    /**
     * 标题
     */
    private String title;
    /**
     * 工单描述
     */
    private String orderDesc;
    /**
     * 创建时间
     */
    private String gmtCreate;
    /**
     * 是否紧急 1:是 0:否
     */
    private int isUrge;
    /**
     * 工单状态
     */
    private String state;
    /**
     * 处理人名称
     */
    private String acceptorName;
    /**
     * 受理组
     */
    private String acceptorGroup;
    /**
     * 受理人工号
     */
    private String acceptorNumber;
    /**
     * 处理人组
     */
    private String handlerGroup;
    /**
     * 处理人工号
     */
    private String handlerNumber;
    /**
     * 处理人姓名
     */
    private String handlerName;
    /**
     * 分派完成时间
     */
    private String gmtDispatchEnd;
    /**
     * 处理完结时间
     */
    private String gmtEnd;
    /**
     * 开始处理时间
     */
    private String gmtBegin;
    /**
     * 开始跟进时间
     */
    private String gmtFollow;
    /**
     * 交接时间
     */
    private String gmtHandover;
    /**
     * 确认时间
     */
    private String gmtConfirm;
    /**
     * 跟进回复内容
     */
    private List<String> followRemark;
    /**
     * 催单次数
     */
    private Integer urgeCount;

    /**
     * 附件列表
     */
    private List<AttachmentDTO> attachments;
    /**
     * 扩展信息
     */
    private Map<String, String> ext = new HashMap<>();

    @Data
    static class AttachmentDTO {
        /**
         * 文件名
         */
        private String fileName;
        /**
         * 后缀名
         */
        private String extensionName;
        /**
         * 文件ceph路径
         */
        private String fileUrl;
        /**
         * yyyy-MM-dd HH:mm:ss
         */
        private String uploadTime;
    }
}
