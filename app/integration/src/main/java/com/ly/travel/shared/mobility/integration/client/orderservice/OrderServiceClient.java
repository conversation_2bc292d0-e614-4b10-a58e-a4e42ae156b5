package com.ly.travel.shared.mobility.integration.client.orderservice;


import com.alibaba.fastjson.JSON;
import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.tcbase.config.AppProfile;
import com.ly.travel.car.orderservice.facade.CarLogFacade;
import com.ly.travel.car.orderservice.facade.model.log.OrderLog;
import com.ly.travel.car.orderservice.facade.request.log.LogQueryRequest;
import com.ly.travel.car.orderservice.facade.request.log.LogSaveRequest;
import com.ly.travel.car.orderservice.facade.response.log.LogQueryResponse;
import com.ly.travel.shared.mobility.integration.enums.LogTypeEnum;
import com.ly.travel.shared.mobility.integration.utils.SerialNoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderServiceClient {

    private static final String LOG_PREFIX = "OrderServiceClient";
    @Resource
    private CarLogFacade carLogFacade;

    @Resource
    private ThreadPoolTaskExecutor carLogSaveTaskPool;


    public List<OrderLog> queryCarLogList(String orderId, String supplierOrderId) {
        List<OrderLog> orderLogList = queryCarLogList(orderId);
        if (StringUtils.isNotBlank(supplierOrderId)) {
            List<OrderLog> supplierOrderLogList = queryCarLogList(supplierOrderId);
            if (CollectionUtils.isNotEmpty(supplierOrderLogList)) {
                orderLogList.addAll(supplierOrderLogList);
            }
        }

        return orderLogList;
    }

    /**
     * 日志列表
     *
     * @param orderId
     * @return
     */
    public List<OrderLog> queryCarLogList(String orderId) {
        LogQueryRequest orderDetailRequest = new LogQueryRequest();
        orderDetailRequest.setOrderSerialNo(orderId);
        orderDetailRequest.setTraceId(SerialNoUtil.genTraceId());
        log.info("{} queryCarLogList request:{}", LOG_PREFIX, JSON.toJSONString(orderDetailRequest));
        LogQueryResponse resp = carLogFacade.query(orderDetailRequest);
        log.info("{} queryCarLogList response:{}", LOG_PREFIX, JSON.toJSONString(resp));

        if (!isSuccess(resp) || CollectionUtils.isEmpty(resp.getLogs())) {
            return new ArrayList<>();
        }
        List<OrderLog> list = resp.getLogs().stream().filter(s -> s.getLogType().equals(LogTypeEnum.RISK_ORDER.name())).sorted(Comparator.comparing((OrderLog s) -> s.getOperatorTime()).reversed()).collect(Collectors.toList());
        return list;
    }


    /**
     * 日志列表
     *
     * @param serialNo
     * @return
     */
    public List<OrderLog> queryCommonLogList(String serialNo, LogTypeEnum logType) {
        LogQueryRequest orderDetailRequest = new LogQueryRequest();
        orderDetailRequest.setOrderSerialNo(serialNo);
        orderDetailRequest.setTraceId(SerialNoUtil.genTraceId());
        log.info("{} queryCarLogList request:{}", LOG_PREFIX, JSON.toJSONString(orderDetailRequest));
        LogQueryResponse resp = carLogFacade.query(orderDetailRequest);
        log.info("{} queryCarLogList response:{}", LOG_PREFIX, JSON.toJSONString(resp));

        if (!isSuccess(resp) || CollectionUtils.isEmpty(resp.getLogs())) {
            return new ArrayList<>();
        }
        List<OrderLog> list = resp.getLogs().stream().filter(s -> s.getLogType().equals(logType.name())).sorted(Comparator.comparing((OrderLog s) -> s.getOperatorTime()).reversed()).collect(Collectors.toList());
        return list;
    }


    public void saveCommonLog(String serialNo, String content, String operator, LogTypeEnum logType) {
        carLogSaveTaskPool.execute(() -> {
            try {
                LogSaveRequest saveRequest = new LogSaveRequest();
                OrderLog orderLog = new OrderLog();
                orderLog.setSerialNo(serialNo);
                orderLog.setLogType(logType.name());
                orderLog.setLogContent(content);
                orderLog.setOperator(operator);
                orderLog.setAppId(AppProfile.getAppName());
                orderLog.setOperatorTime(System.currentTimeMillis());

                saveRequest.setLogs(Arrays.asList(orderLog));
                saveRequest.setTraceId(SerialNoUtil.genTraceId());
                carLogFacade.save(saveRequest);
            } catch (Exception e) {
                log.error("", e);
            }
        });
    }


    private boolean isSuccess(BaseResponseDTO resp) {
        return resp != null && resp.isSuccess();
    }

}
