package com.ly.travel.shared.mobility.integration.configcenter;

import com.ly.sof.utils.mapping.JacksonUtils;
import com.ly.tcbase.config.AppProfile;
import com.ly.travel.shared.mobility.integration.utils.OkHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
public class ConfigCenterApi {
    private static final String URL_MODIFY_AND_PUSH_CONFIG = "http://tccomponent.17usoft.com/tcconfigcenter6/v6/modifyandpushconfig";
    private static final String URL_MODIFY_AND_PUSH_CONFIG_LIST = "http://tccomponent.17usoft.com/tcconfigcenter6/v6/modifyandpushconfiglist";
    private static final String CHARSET = "UTF-8";
    private static final String ADD = "add";
    private static final String UPDATE = "update";
    private static final String DELETE = "delete";

    public ConfigCenterApi() {
    }

    public static boolean updateAndPushConfig(String projectName, String key, String value, String modifiedBy,String env) {
        return modifyAndPushConfig(projectName, key, value, modifiedBy, true, env, projectName, "update");
    }

    public static boolean modifyAndPushConfig(String projectName, String key, String value, String modifiedBy, boolean open, String env, String password, String operation) {
        ConfigInfoEntity configInfo = new ConfigInfoEntity();
        configInfo.setProjectName(projectName);
        configInfo.setKey(key);
        configInfo.setValue(value);
        configInfo.setCacheType(operation);
        configInfo.setUserName(modifiedBy);
        configInfo.setOpen(open);
        configInfo.setEnv(env);
        String request = JacksonUtils.toJSONString(configInfo);
        log.info("统一配置modifyAndPushConfig request:" + request);
        String url = "http://tccomponent.17usoft.com/tcconfigcenter6/v6/modifyandpushconfig";

        String response = null;
        try {
            response = OkHttpUtils.postJson(url, request, getHttpHeader(projectName, password), 10000, TimeUnit.MILLISECONDS);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        log.info("统一配置modifyAndPushConfig response:" + response);
        return true;
    }

    private static Map<String, String> getHttpHeader(String projectName, String password) {
        Map<String, String> headers = new HashMap<>(2);
        String usernamePassword = projectName + ":" + password;
        String base64 = encodeBase64(usernamePassword);
        String author = "Basic " + base64;
        headers.put("Authorization", author);
        headers.put("Cache-Control", "no-cache");
        return headers;
    }

    private static String encodeBase64(String input) {
        return new String(Base64.encodeBase64(input.getBytes(StandardCharsets.UTF_8)));
    }
}
