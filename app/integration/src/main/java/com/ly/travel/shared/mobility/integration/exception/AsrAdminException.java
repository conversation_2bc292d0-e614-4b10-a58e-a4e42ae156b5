package com.ly.travel.shared.mobility.integration.exception;


import com.ly.travel.shared.mobility.integration.enums.AsrAdminErrorEnum;

public class AsrAdminException extends RuntimeException {
    private int code;
    private String message;

    public AsrAdminException(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public AsrAdminException(String message, Throwable cause) {
        super(message, cause);
    }

    public AsrAdminException(String message) {
        super(message);
        this.code = AsrAdminErrorEnum.BUSINESS_ERROR.getCode();
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
