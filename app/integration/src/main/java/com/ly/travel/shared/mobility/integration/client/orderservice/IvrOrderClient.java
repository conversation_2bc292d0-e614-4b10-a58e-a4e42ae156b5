package com.ly.travel.shared.mobility.integration.client.orderservice;

import com.ly.travel.car.orderservice.facade.request.ivr.IVROrderDetailRequest;
import com.ly.travel.car.orderservice.facade.request.ivr.IVROrderListRequest;
import com.ly.travel.car.orderservice.facade.response.ivr.IVROrderDetailResponse;
import com.ly.travel.car.orderservice.facade.response.ivr.IVROrderListResponse;

/**
 * @Description: 订单服务查询Client
 * @Author: jay.he
 * @Date: 2025-08-19 10:51
 * @Version: 1.0
 **/
public interface IvrOrderClient {

    /**
     * 查询IVR订单列表
     *
     * @param request
     * @return
     */
    IVROrderListResponse queryIvrOrderList(IVROrderListRequest request);

    /**
     * 订单详情
     *
     * @param request
     * @return
     */
    IVROrderDetailResponse orderDetail(IVROrderDetailRequest request);

}
