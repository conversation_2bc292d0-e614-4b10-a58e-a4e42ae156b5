<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.ly.travel.shared.mobility</groupId>
        <artifactId>shared-mobility-asr-admin-parent</artifactId>
        <version>*******</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shared-mobility-asr-admin-integration</artifactId>
    <packaging>jar</packaging>

    <name>LY shared-mobility-asr-admin-integration</name>
    <description>LY shared-mobility-asr-admin-integration</description>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <dependencies>
        <!--region log dependecies -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <!-- log4j2 -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-web</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!--endregion -->

        <dependency>
            <groupId>com.ly.flight.toolkit</groupId>
            <artifactId>object-diff</artifactId>
        </dependency>
        <!-- 热部署组件依赖redis 依赖tcbase cache后开启 -->
        <!--		<dependency>-->
        <!--			<groupId>com.ly.flight.toolkit</groupId>-->
        <!--			<artifactId>deploy-static-resource</artifactId>-->
        <!--		</dependency>-->

        <!--region Test dependecies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ly.sof</groupId>
            <artifactId>sof-api</artifactId>
        </dependency>
        <!--endregion -->

        <dependency>
            <groupId>com.ly.travel.shared.mobility.supply</groupId>
            <artifactId>crm-client</artifactId>
            <version>1.0.54.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-order-service-facade</artifactId>
            <version>1.0.4.2.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.ly.dsf</groupId>
            <artifactId>dsf-tccommon</artifactId>
            <version>2.5.14</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.12</version>
        </dependency>
        <dependency>
            <groupId>com.ly.tcbase</groupId>
            <artifactId>configcenterclient</artifactId>
            <version>6.2.8</version>
        </dependency>
        <dependency>
            <groupId>net.sf.dozer</groupId>
            <artifactId>dozer</artifactId>
            <version>5.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-order-core-facade</artifactId>
        </dependency>

    </dependencies>
</project>
