package com.ly.travel.shared.mobility.web.controller.common;

import com.ly.sof.utils.page.PageList;
import com.ly.travel.shared.mobility.biz.model.resp.AsrAdminBaseResp;
import com.ly.travel.shared.mobility.supply.crm.client.repository.BasicDataCityRepository;
import com.ly.travel.shared.mobility.supply.crm.client.repository.SupplierBaseInfoRepository;
import com.ly.travel.shared.mobility.supply.crm.core.model.dto.BasicDataCityDTO;
import com.ly.travel.shared.mobility.supply.crm.core.model.dto.SupplierBaseInfoDTO;
import com.ly.travel.shared.mobility.supply.crm.core.model.enums.CityLevelEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("commondata")
public class CommonDataController {
    @Resource
    private SupplierBaseInfoRepository supplierBaseInfoRepository;
    @Resource
    private BasicDataCityRepository basicDataCityRepository;


    /**
     * 供应商列表
     *
     * @param
     * @return
     */
    @RequestMapping("supplierList")
    public AsrAdminBaseResp<PageList<SupplierBaseInfoDTO>> supplierList(String keyword) {
        List<SupplierBaseInfoDTO> list = supplierBaseInfoRepository.listSuppliers(null);
        if (StringUtils.isNotBlank(keyword)) {
            List<String> supplierCodeList = Arrays.asList(keyword.split(","));
            list = list.stream()
                    .filter(s -> supplierCodeList.stream().anyMatch(p->StringUtils.containsIgnoreCase(p,s.getSupplierCode())) || StringUtils.containsIgnoreCase(s.getSupplierName(), keyword))
                    .sorted((d1, d2) -> {
                        Integer index1 = 0;
                        Integer index2 = 0;
                        Integer leftSize1 = 0;
                        Integer leftSize2 = 0;
                        if (StringUtils.containsIgnoreCase(d1.getSupplierCode(), keyword)) {
                            index1 = d1.getSupplierCode().toLowerCase().indexOf(keyword.toLowerCase());
                            leftSize1 = d1.getSupplierCode().length() - keyword.length();
                        } else {
                            index1 = d1.getSupplierName().toLowerCase().indexOf(keyword.toLowerCase());
                            leftSize1 = d1.getSupplierName().length() - keyword.length();
                        }
                        if (StringUtils.containsIgnoreCase(d2.getSupplierCode(), keyword)) {
                            index2 = d2.getSupplierCode().toLowerCase().indexOf(keyword.toLowerCase());
                            leftSize2 = d2.getSupplierCode().length() - keyword.length();
                        } else {
                            index2 = d2.getSupplierName().toLowerCase().indexOf(keyword.toLowerCase());
                            leftSize2 = d2.getSupplierName().length() - keyword.length();
                        }

                        int result = index1.compareTo(index2);
                        if (result == 0) {
                            result = leftSize1.compareTo(leftSize2);
                        }
                        return result;
                    })
                    .collect(Collectors.toList());
        }
        return AsrAdminBaseResp.ok(list);
    }



    @GetMapping("cityList")
    public AsrAdminBaseResp<List<BasicDataCityDTO>> cityList() {
        List<BasicDataCityDTO> list = basicDataCityRepository.getAllCity();
        list = list.stream().filter(s -> !s.getTcId().equals(0) && CityLevelEnum.CITY.getCode().equals(s.getLevel())).collect(Collectors.toList());
        return AsrAdminBaseResp.ok(list);
    }
}
