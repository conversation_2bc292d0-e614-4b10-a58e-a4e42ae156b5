package com.ly.travel.shared.mobility.web.controller.sound;

import com.ly.travel.shared.mobility.biz.model.dto.OrderSoundAuditDTO;
import com.ly.travel.shared.mobility.biz.model.dto.OrderSoundDTO;
import com.ly.travel.shared.mobility.biz.model.req.SoundPageQueryReq;
import com.ly.travel.shared.mobility.biz.model.req.SoundTextQueryReq;
import com.ly.travel.shared.mobility.biz.model.resp.AsrAdminBaseResp;
import com.ly.travel.shared.mobility.biz.model.resp.OrderSoundToTextResp;
import com.ly.travel.shared.mobility.biz.model.resp.SoundPageQueryResp;
import com.ly.travel.shared.mobility.biz.service.SoundManageService;
import com.ly.travel.shared.mobility.dal.mybatisplus.utils.UserInfoUtils;
import com.ly.travel.shared.mobility.supply.authentication.annotation.AuthenticationOptions;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/19
 */
@RestController
@RequestMapping("sound")
public class SoundManageController {

    @Resource
    private SoundManageService soundManageService;

    @PostMapping("queryPage")
    public AsrAdminBaseResp<SoundPageQueryResp> queryPage(@RequestBody SoundPageQueryReq req) {

        AsrAdminBaseResp<SoundPageQueryResp> resp = new AsrAdminBaseResp<>();
        resp.setData(soundManageService.queryPage(req));
        return resp;
    }

    @PostMapping("audit")
    @AuthenticationOptions(requireResources = { "asr:sound:audit" }, authenticationFailureMapperBeanName = "singleResultAuthFailurePostProcessor")
    public AsrAdminBaseResp<?> audit(@RequestBody OrderSoundAuditDTO req) {

        soundManageService.audit(req, UserInfoUtils.getUser());
        return AsrAdminBaseResp.ok();
    }

    @PostMapping("querySoundToText")
    public AsrAdminBaseResp<OrderSoundToTextResp> querySoundToText(@RequestBody SoundTextQueryReq req) {
        AsrAdminBaseResp<OrderSoundToTextResp> resp = new AsrAdminBaseResp<>();
        resp.setData(soundManageService.querySoundToText(req));
        return resp;
    }
}
