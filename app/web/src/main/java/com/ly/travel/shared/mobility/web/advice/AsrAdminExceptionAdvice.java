
package com.ly.travel.shared.mobility.web.advice;


import com.ly.travel.shared.mobility.biz.model.resp.AsrAdminBaseResp;
import com.ly.travel.shared.mobility.integration.enums.AsrAdminErrorEnum;
import com.ly.travel.shared.mobility.integration.exception.AsrAdminException;
import com.ly.travel.shared.mobility.integration.exception.AsrAdminWarnException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ValidationException;


@Slf4j
@RestControllerAdvice(basePackages = "com.ly.travel.shared.mobility.web.controller")
public class AsrAdminExceptionAdvice {
    private static final String LOG_PRINT_TEMPLATE = "AsrAdminExceptionAdvice:{}";

    /**
     * 捕获 {@code AsrAdminException} 异常
     */
    @ExceptionHandler({AsrAdminException.class})
    public AsrAdminBaseResp<?> handleAsrAdminException(AsrAdminException ex) {
        log.error(LOG_PRINT_TEMPLATE, ex.getMessage());
        return AsrAdminBaseResp.fail(ex.getCode(), ex.getMessage());
    }



    @ExceptionHandler({ValidationException.class})
    public AsrAdminBaseResp<?> handleValidationException(ValidationException ex) {
        log.error(LOG_PRINT_TEMPLATE, ex.getMessage());
        return AsrAdminBaseResp.fail(AsrAdminErrorEnum.PARAM_ERROR.getCode(),ex.getMessage());
    }

    /**
     * 警告异常异常
     */
    @ExceptionHandler({AsrAdminWarnException.class})
    public AsrAdminBaseResp<?> handle(AsrAdminWarnException ex) {
        log.warn(LOG_PRINT_TEMPLATE, ex.getMessage());
        return AsrAdminBaseResp.fail(ex.getCode(), ex.getMessage());
    }



    /**
     * 顶级异常捕获并统一处理，当其他异常无法处理时候选择使用
     */
    @ExceptionHandler({Exception.class})
    public AsrAdminBaseResp<?> handle(Exception ex) {
        ex.printStackTrace();
        Throwable cause = ex.getCause();
        if (cause != null) {
            log.error(LOG_PRINT_TEMPLATE, cause);
            return AsrAdminBaseResp.fail(cause.getMessage());
        }
        String message = ex.getMessage();
        if(message != null) {
            log.error(LOG_PRINT_TEMPLATE, ex);
            return AsrAdminBaseResp.fail(message);
        }
        log.error(LOG_PRINT_TEMPLATE, ex);
        return AsrAdminBaseResp.fail(ex.toString());
    }


}
