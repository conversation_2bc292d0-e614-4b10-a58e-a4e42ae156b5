package com.ly.travel.shared.mobility.web.controller.account;

import com.ly.travel.shared.mobility.biz.model.dto.AccountDTO;
import com.ly.travel.shared.mobility.biz.model.req.AccountPageQueryReq;
import com.ly.travel.shared.mobility.biz.model.resp.AccountPageQueryResp;
import com.ly.travel.shared.mobility.biz.model.resp.AsrAdminBaseResp;
import com.ly.travel.shared.mobility.biz.service.AccountManageService;
import com.ly.travel.shared.mobility.dal.mybatisplus.utils.UserInfoUtils;
import com.ly.travel.shared.mobility.supply.authentication.annotation.AuthenticationOptions;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/18
 */
@RestController
@RequestMapping("account")
public class AccountManageController {

    @Resource
    private AccountManageService accountManageService;
    @PostMapping("queryPage")
    public AsrAdminBaseResp<AccountPageQueryResp> queryPage(@RequestBody AccountPageQueryReq req) {

        AsrAdminBaseResp<AccountPageQueryResp> resp = new AsrAdminBaseResp<>();
        resp.setData(accountManageService.queryPage(req));
        return resp;
    }

    @PostMapping("add")
//    @AuthenticationOptions(requireResources = {"asr:account:save"}, authenticationFailureMapperBeanName = "singleResultAuthFailurePostProcessor")
    public AsrAdminBaseResp<?> add(@RequestBody AccountDTO req) {

        accountManageService.add(req, UserInfoUtils.getUser());
        return AsrAdminBaseResp.ok();
    }

    @PostMapping("update")
//    @AuthenticationOptions(requireResources = {"asr:account:save"}, authenticationFailureMapperBeanName = "singleResultAuthFailurePostProcessor")
    public AsrAdminBaseResp<?> update(@RequestBody AccountDTO req) {

        accountManageService.update(req, UserInfoUtils.getUser());
        return AsrAdminBaseResp.ok();
    }
}
