package com.ly.travel.shared.mobility.web.controller.userinfo;

import com.ly.travel.shared.mobility.biz.model.resp.AsrAdminBaseResp;
import com.ly.travel.shared.mobility.dal.mybatisplus.utils.UserInfoUtils;
import com.ly.travel.shared.mobility.supply.authentication.annotation.AuthenticationOptions;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("userinfo")
public class UserInfoController {
    /**
     * 查询权限列表
     * @return
     */
    @PostMapping("queryPermissionList")
    @AuthenticationOptions(requireResources = {"AsrAdmin:userInfo:permission"}, authenticationFailureMapperBeanName = "responseAuthFailurePostProcessor")
    public AsrAdminBaseResp<List<String>> queryPermissionList() {
        return AsrAdminBaseResp.ok(UserInfoUtils.getPermissionList());
    }
}
