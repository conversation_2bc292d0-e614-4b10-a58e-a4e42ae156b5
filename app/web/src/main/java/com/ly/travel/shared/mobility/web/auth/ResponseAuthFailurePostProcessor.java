package com.ly.travel.shared.mobility.web.auth;

import com.ly.travel.shared.mobility.biz.model.resp.AsrAdminBaseResp;
import com.ly.travel.shared.mobility.integration.enums.AsrAdminErrorEnum;
import com.ly.travel.shared.mobility.supply.authentication.model.AuthenticationFailureReasonEnum;
import com.ly.travel.shared.mobility.supply.authentication.model.UserInfo;
import com.ly.travel.shared.mobility.supply.authentication.strategy.AuthenticationFailurePostProcessor;
import org.springframework.stereotype.Component;


@Component("responseAuthFailurePostProcessor")
public class ResponseAuthFailurePostProcessor implements AuthenticationFailurePostProcessor {

    @Override
    public Object buildResultWhenAuthenticationFailure(AuthenticationFailureReasonEnum authenticationFailureReason, UserInfo userInfo) {
        AsrAdminBaseResp resp = new AsrAdminBaseResp();
       resp.setErrCode(AsrAdminErrorEnum.AUTH_ERROR.getCode());
       resp.setMsg(authenticationFailureReason.getErrorMessage());
       return  resp;
    }
}
