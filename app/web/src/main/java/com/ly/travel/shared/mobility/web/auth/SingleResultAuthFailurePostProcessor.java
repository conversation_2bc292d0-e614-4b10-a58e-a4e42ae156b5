package com.ly.travel.shared.mobility.web.auth;

import com.ly.travel.shared.mobility.biz.model.resp.AsrAdminBaseResp;
import com.ly.travel.shared.mobility.supply.authentication.model.AuthenticationFailureReasonEnum;
import com.ly.travel.shared.mobility.supply.authentication.model.UserInfo;
import com.ly.travel.shared.mobility.supply.authentication.strategy.AuthenticationFailurePostProcessor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/5 14:25
 */
@Component("singleResultAuthFailurePostProcessor")
public class SingleResultAuthFailurePostProcessor implements AuthenticationFailurePostProcessor {
    @Override
    public Object buildResultWhenAuthenticationFailure(AuthenticationFailureReasonEnum authenticationFailureReason, UserInfo userInfo) {
        AsrAdminBaseResp resp = new AsrAdminBaseResp();
        resp.setErrCode(Integer.parseInt(authenticationFailureReason.getErrorCode()));
        resp.setMsg(authenticationFailureReason.getErrorMessage());
        return resp;
    }

}
