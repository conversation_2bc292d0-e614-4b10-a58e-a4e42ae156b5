<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.ly.travel.shared.mobility</groupId>
        <artifactId>shared-mobility-asr-admin-parent</artifactId>
        <version>*******</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shared-mobility-asr-admin-biz</artifactId>
    <packaging>jar</packaging>

    <name>LY shared-mobility-asr-admin-biz</name>
    <description>LY shared-mobility-asr-admin-biz</description>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.ly.travel.shared.mobility</groupId>
            <artifactId>shared-mobility-asr-admin-integration</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.shared.mobility</groupId>
            <artifactId>shared-mobility-asr-admin-dal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.sof</groupId>
            <artifactId>sof-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <!-- form basic -->
        <dependency>
            <artifactId>sof-batis-gen-dependency</artifactId>
            <groupId>com.ly.flight.toolkit</groupId>
        </dependency>
        <!--region Test dependecies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.9.0</version>
        </dependency>
        <!--endregion -->

        <dependency>
            <groupId>com.github.junrar</groupId>
            <artifactId>junrar</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
        </dependency>

        <!--用车订单列表查询-->
        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-order-service-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-order-service-model</artifactId>
        </dependency>
    </dependencies>
</project>
