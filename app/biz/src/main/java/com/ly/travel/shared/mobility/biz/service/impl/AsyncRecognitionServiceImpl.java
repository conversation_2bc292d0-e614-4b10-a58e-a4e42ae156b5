package com.ly.travel.shared.mobility.biz.service.impl;

import com.ly.sof.utils.mapping.JacksonUtils;
import com.ly.tcbase.websocket.client.WebSocketClient;
import com.ly.tcbase.websocket.handshake.ServerHandshake;
import com.ly.travel.shared.mobility.biz.model.vo.meta.FileMetaInfo;
import com.ly.travel.shared.mobility.biz.service.AsyncRecognitionService;
import com.ly.travel.shared.mobility.biz.utils.FileUtils;
import com.ly.travel.shared.mobility.biz.utils.S3ClientApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

/**
 * @Description: 异步语音识别
 * @Author: jay.he
 * @Date: 2025-08-18 19:26
 * @Version: 1.0
 **/
@Slf4j
@Service
public class AsyncRecognitionServiceImpl implements AsyncRecognitionService {

    @Value("${funasr.server.url}")
    private String funasrServerUrl;

    @Value("${upload.file.temp.path}")
    private String uploadTempPath;

    @Resource
    private S3ClientApi s3ClientApi;


    @Override
    public String unzipAndAsyncRecognize(String taskId, MultipartFile file) throws IOException {

        // 1.保存原始文件
        String fileName = file.getOriginalFilename();
        String savePath = uploadTempPath + taskId + "_*_" + fileName;
        file.transferTo(new File(savePath));
        log.info("[AsyncRecognitionService] saveOriginalFile, path = {}", savePath);

        // 2.原文件上传S3
        String s3Url = uploadToS3(savePath);
        log.info("[AsyncRecognitionService] upload to S3，s3-url:{}", s3Url);

        // 3.解压原文件并获取音视频文件元数据列表
        List<FileMetaInfo> fileMetadataList = FileUtils.extractAndListMediaFiles(savePath);
        log.info("[AsyncRecognitionService] extractAndListMediaFiles, fileMetadataList = {}",
                JacksonUtils.toJSONString(fileMetadataList));

        // 4.异步识别
        log.info("[AsyncRecognitionService] start async recognition ");
        CompletableFuture.runAsync(() -> {
            recognize(taskId, fileMetadataList);
        });

        return s3Url;
    }

    private String uploadToS3(String savePath) {
        File file = new File(savePath);
        if (!file.exists()) {
            log.error("File not found: {}", savePath);
            throw new RuntimeException("File not found: " + savePath);
        }

        try (InputStream inputStream = new BufferedInputStream(new FileInputStream(file))) {
            // 获取文件名
            String fileName = file.getName();

            // 调用S3客户端API上传文件
            s3ClientApi.putObjByStream(fileName, inputStream);

            // 获取上传的S3文件url
            String s3Url = s3ClientApi.getFileUrl(fileName);
            if (s3Url == null || s3Url.isEmpty()) {
                throw new RuntimeException("S3 upload returned empty URL");
            }

            log.info("Successfully uploaded file to S3: {}", s3Url);
            return s3Url;

        } catch (Exception e) {
            log.error("Failed to upload file to S3: {}", savePath, e);
            return null;
        }
    }


    @Override
    public void recognize(String taskId, String filePath) {

        CountDownLatch latch = new CountDownLatch(1); // 创建计数器
        WebSocketClient client = null;
        try {
            client = new WebSocketClient(new URI(funasrServerUrl)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    log.info("Connected to FunASR");
                }

                @Override
                public void onMessage(String message) {
                    // FunASR 返回结果 JSON
                    String resultText = parseResult(taskId, message);
                    latch.countDown(); // 收到消息后计数器减1
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.info("Closed: reason:{}, time:{}", reason, new Date().toLocaleString());
                    latch.countDown(); // 连接关闭时也释放等待
                    //删除临时文件
                    delTempFile(filePath);
                }

                @Override
                public void onError(Exception ex) {
                    log.info("任务执行fail，taskId={}", taskId);
                    ex.printStackTrace();
                    latch.countDown(); // 出错时也释放等待
                    //删除临时文件
                    delTempFile(filePath);
                }
            };

            client.connectBlocking();

            // 发送控制 JSON
            String controlJson = String.format(
                    "{\"mode\":\"offline\",\"wav_name\":\"%s\",\"wav_format\":\"wav\"}",
                    new File(filePath).getName()
            );
            client.send(controlJson);

            // 发送音频数据
            byte[] audioBytes = Files.readAllBytes(Paths.get(filePath));
            client.send(audioBytes);

            // 发送结束标志
            client.send("{\"is_speaking\": false}");

            //方式2：使用CountDownLatch，返回结果后，就关闭WebSocket连接
            // 等待收到消息或连接关闭
            latch.await(); // 阻塞等待，直到计数器归零

        } catch (Exception e) {
            log.error("任务执行失败，taskId={}, error:", taskId, e);
        } finally {
            if (client != null && client.isOpen()) {
                client.close();
            }
        }
    }

    private void delTempFile(String filePath) {
        FileUtils.delFile(filePath);
    }

    @Override
    public void recognize(String taskId, List<FileMetaInfo> fileMetadataList) {
        if (CollectionUtils.isEmpty(fileMetadataList)) {
            return; // 如果列表为空，直接返回
        }
        for (FileMetaInfo fileMetaInfo : fileMetadataList) {
            CompletableFuture.runAsync(() -> {
                recognize(taskId + "-*-" + fileMetaInfo.getFileName(), fileMetaInfo.getFilePath());
            });
        }
    }

    private String parseResult(String taskId, String message) {
        log.info("语音识别结果：result:{}", message);
        return message;
    }
}
