package com.ly.travel.shared.mobility.biz.service;

import com.ly.travel.shared.mobility.biz.model.vo.order.OrderInfoVO;
import com.ly.travel.shared.mobility.biz.model.vo.order.OrderQueryVO;

import java.util.List;

/**
 * @Description: 订单查询服务
 * @Author: jay.he
 * @Date: 2025-08-19 11:10
 * @Version: 1.0
 **/
public interface OrderService {

    /**
     * 查询顺风车订单列表
     *
     * @param req
     * @return
     */
    List<OrderInfoVO> querySfcOrderList(OrderQueryVO req);

    List<OrderInfoVO> queryFinishOrderList(OrderQueryVO req);
}
