package com.ly.travel.shared.mobility.biz.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 业务类型所属组*
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/08 15:21
 */
public enum ProductTypeGroupEnum {

    /**
     * 网约车
     */
    ONLINE_SPECIAL_CAR(1, "网约车"),
    /**
     * 接送机&接送站
     */
    AIRPORT_AND_STATION(2, "接送机&接送站"),
    /**
     * 顺风车
     */
    SFC(3, "顺风车"),
    /**
     * 汽车票
     */
    BUS(4, "汽车票"),
    /**
     * 巴士
     */
    SCENIC(5, "巴士"),

    UNKNOWN(99, "未知"),


    ;

    private final Integer value;

    private final String desc;

    private static final Map<Integer, ProductTypeGroupEnum> VALUE_MAP = new HashMap<>();

    static {
        Arrays.stream(ProductTypeGroupEnum.values()).forEach(e -> VALUE_MAP.put(e.getValue(), e));
    }

    ProductTypeGroupEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static Map<Integer, ProductTypeGroupEnum> getValueMap() {
        return VALUE_MAP;
    }

    @JsonCreator
    public static ProductTypeGroupEnum fromValue(Integer value) {
        return VALUE_MAP.get(value);
    }
    public static String ofDesc(Integer value) {
        ProductTypeGroupEnum productTypeGroupEnum = VALUE_MAP.get(value);
        if (productTypeGroupEnum == null) {
            return "";
        }
        return productTypeGroupEnum.getDesc();
    }
}
