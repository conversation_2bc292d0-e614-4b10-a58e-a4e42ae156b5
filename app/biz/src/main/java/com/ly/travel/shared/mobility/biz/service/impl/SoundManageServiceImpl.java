package com.ly.travel.shared.mobility.biz.service.impl;

import cn.hutool.core.net.URLDecoder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.dal.util.DateUtil;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.shared.mobility.biz.enums.AsrOrderAuditStatusEnum;
import com.ly.travel.shared.mobility.biz.enums.AsrOrderUploadStatusEnum;
import com.ly.travel.shared.mobility.biz.enums.ProductTypeEnum;
import com.ly.travel.shared.mobility.biz.enums.ProductTypeGroupEnum;
import com.ly.travel.shared.mobility.biz.model.dto.*;
import com.ly.travel.shared.mobility.biz.model.req.SoundPageQueryReq;
import com.ly.travel.shared.mobility.biz.model.req.SoundTextQueryReq;
import com.ly.travel.shared.mobility.biz.model.resp.AsrAdminPageBaseResp;
import com.ly.travel.shared.mobility.biz.model.resp.OrderSoundToTextResp;
import com.ly.travel.shared.mobility.biz.model.resp.SoundPageQueryResp;
import com.ly.travel.shared.mobility.biz.model.vo.order.OrderInfoVO;
import com.ly.travel.shared.mobility.biz.model.vo.order.OrderQueryVO;
import com.ly.travel.shared.mobility.biz.service.AccountManageService;
import com.ly.travel.shared.mobility.biz.service.OrderService;
import com.ly.travel.shared.mobility.biz.service.SoundManageService;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao.AsrOrderInfoDao;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao.AsrRecognitionRecordDao;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrOrderInfoDO;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrRecognitionRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/19
 */
@Service
@Slf4j
public class SoundManageServiceImpl implements SoundManageService {

    @Resource
    private AsrOrderInfoDao asrOrderInfoDao;
    @Resource
    private AccountManageService accountManageService;
    @Resource
    private OrderService orderService;
    @Resource
    private AsrRecognitionRecordDao recognitionRecordDao;
    @Value("${sof-env}")
    private String env;

    @Override
    public SoundPageQueryResp queryPage(SoundPageQueryReq req) {
        //同步所有账号的订单列表
        syncAllAccountOrder();

        QueryWrapper<AsrOrderInfoDO> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(req.getOrderSerialNo()), "order_serial_no", StringUtils.trim(req.getOrderSerialNo()));
        wrapper.eq(StringUtils.isNotBlank(req.getMobileNo()), "mobile_no", StringUtils.trim(req.getMobileNo()));
        wrapper.like(StringUtils.isNotBlank(req.getVehicleNo()), "plate_no", StringUtils.trim(req.getVehicleNo()));
        wrapper.eq(Objects.nonNull(req.getUploadStatus()), "upload_status", req.getUploadStatus());
        wrapper.eq(Objects.nonNull(req.getAuditStatus()), "audit_status", req.getAuditStatus());
        wrapper.eq(Objects.nonNull(req.getProductType()), "order_type", req.getProductType());
        wrapper.eq("env", env);
        wrapper.ge(StringUtils.isNotBlank(req.getStartTime()),"trip_start_time", DateUtil.string2Date(req.getStartTime()));
        wrapper.le(StringUtils.isNotBlank(req.getEndTime()),"trip_end_time", DateUtil.string2Date(req.getEndTime()));
        wrapper.orderByDesc("id");

        Page<AsrOrderInfoDO> page = new Page<>(req.getPage(), req.getPageSize());
        Page<AsrOrderInfoDO> pageResult = asrOrderInfoDao.getBaseMapper().selectPage(page, wrapper);

        SoundPageQueryResp resp = new SoundPageQueryResp();
        resp.setPaginator(new AsrAdminPageBaseResp.Paginator(pageResult.getTotal()));
        List<AsrOrderInfoDO> records = pageResult.getRecords();
        List<OrderSoundDTO> resultList = records.stream().map(item -> {
            OrderSoundDTO dto = new OrderSoundDTO();
            BeanUtils.copyProperties(item, dto);
            dto.setFileName(getFileName(item.getFileUrl()));
            ProductTypeEnum productTypeEnum = ProductTypeEnum.fromValue(item.getOrderType());
            if (productTypeEnum != null){
                String groupDesc = ProductTypeGroupEnum.ofDesc(productTypeEnum.getProductTypeGroup());
                if (groupDesc.equals(productTypeEnum.getDesc())){
                    dto.setOrderTypeDesc(productTypeEnum.getDesc());
                }else {
                    dto.setOrderTypeDesc( groupDesc + ": " + productTypeEnum.getDesc());
                }

            }
            if (StringUtils.isNotBlank(item.getServiceTag())){
                dto.setServiceTag(item.getServiceTag().replaceAll(",","、"));
            }
            return dto;
        }).collect(Collectors.toList());
        resp.setData(resultList);
        return resp;
    }

    @Override
    public void audit(OrderSoundAuditDTO req, String user) {
        if (req.getId() == null){
            throw new RuntimeException("请选择要审核的订单");
        }
        AsrOrderInfoDO updateEntity = new AsrOrderInfoDO();
        updateEntity.setId(req.getId());
        updateEntity.setAuditStatus(AsrOrderAuditStatusEnum.WAIT_AUDIT.getStatus());
        if (Objects.equals(req.getUploadStatus(), AsrOrderUploadStatusEnum.QUALIFICATION.getStatus())){
            updateEntity.setAuditStatus(AsrOrderAuditStatusEnum.HAD_AUDIT.getStatus());
        }
        updateEntity.setUploadStatus(req.getUploadStatus());
        updateEntity.setDriverViolated(req.getDriverViolated());
        updateEntity.setAuditComment(req.getAuditComment());
        updateEntity.setDriverScore(req.getDriverScore());
        updateEntity.setUpdateBy(user);
        updateEntity.setUpdateTime(new Date());
        asrOrderInfoDao.getBaseMapper().updateById(updateEntity);
    }

    /**
     * 查询录音转文字结果
     * @param req 订单号
     * @return 录音转文字结果
     */
    @Override
    public OrderSoundToTextResp querySoundToText(SoundTextQueryReq req) {
        QueryWrapper<AsrRecognitionRecordDO> wrapper = new QueryWrapper<AsrRecognitionRecordDO>()
                .eq("order_no", req.getOrderSerialNo())
                .eq("status", 0)
                .eq("env", env);
        wrapper.orderByAsc("seq_no");
        List<AsrRecognitionRecordDO> list = recognitionRecordDao.getBaseMapper().selectList(wrapper);
        OrderSoundToTextResp resp = new OrderSoundToTextResp();
        if (CollectionUtils.isNotEmpty(list)){
            resp.setData(list.stream().map(item -> {
                OrderSoundToTextDTO orderSoundToTextDTO = new OrderSoundToTextDTO();
                orderSoundToTextDTO.setText(item.getResult());
                orderSoundToTextDTO.setResourceUrl(item.getResourceUrl());
                FileMetaInfoDTO fileMetaInfoDTO = FastJsonUtils.fromJSONString(item.getMetaInfo(), FileMetaInfoDTO.class);
                orderSoundToTextDTO.setFileName(fileMetaInfoDTO.getFileName());
                orderSoundToTextDTO.setSeqNo(item.getSeqNo());
                return orderSoundToTextDTO;
            }).collect(Collectors.toList()));
        }
        return resp;
    }

    /**
     * 同步所有账号最新的订单列表信息
     */
    private void syncAllAccountOrder(){
        List<OrderInfoVO> allOrderList = new ArrayList<>();
        List<AccountDTO> accountList = accountManageService.getAccountList();
        accountList = accountList.stream().filter(item -> StringUtils.isNotBlank(item.getMobile()) && !Objects.equals(item.getStatus(), 0)).collect(Collectors.toList());

        String startDay = DateUtil.date2String(DateUtil.addDay(new Date(), -90));
        String endDay = DateUtil.date2String(DateUtil.getDayEndDate(new Date()));
        String traceId = UUID.randomUUID().toString();
        //多线程去请求每个账号的订单列表
        List<CompletableFuture<List<OrderInfoVO>>> futureList = new ArrayList<>();
        for (AccountDTO account : accountList) {
            futureList.add(CompletableFuture.supplyAsync(() -> {
                OrderQueryVO orderQueryVO = new OrderQueryVO();
                orderQueryVO.setPhoneNo(account.getMobile());
                orderQueryVO.setStartTime(startDay);
                orderQueryVO.setEndTime(endDay);
                orderQueryVO.setTraceId(traceId);
                List<OrderInfoVO> list = orderService.queryFinishOrderList(orderQueryVO);
                if (CollectionUtils.isNotEmpty(list)){
                    list.forEach(e -> e.setMobileNo(account.getMobile()));
                }
                return list;
            }));
        }
        CompletableFuture<List<OrderInfoVO>>[] futureArray = futureList.toArray(new CompletableFuture[]{});
        CompletableFuture.allOf(futureArray).join();
        for (CompletableFuture<List<OrderInfoVO>> future : futureArray) {
            try {
                List<OrderInfoVO> orderList = future.get();
                if (CollectionUtils.isNotEmpty(orderList)){
                    allOrderList.addAll(orderList);
                }
            } catch (Exception e) {
                log.error("多线程获取账号顺风车订单列表异常",e);
            }
        }

        if (CollectionUtils.isEmpty(allOrderList)){
            return;
        }

        //获取表中的所有订单，比较哪些订单需要新增同步
        List<AsrOrderInfoDO> dbOrderList = asrOrderInfoDao.getBaseMapper().selectList(new QueryWrapper<AsrOrderInfoDO>().eq("env", env));
        Map<String, AsrOrderInfoDO> dbOrderMap = dbOrderList.stream().collect(Collectors.toMap(AsrOrderInfoDO::getOrderSerialNo, item -> item, (oldItem, newItem) -> newItem));
        for (OrderInfoVO userOrder : allOrderList) {
            AsrOrderInfoDO dbOrder = dbOrderMap.get(userOrder.getOrderNo());
            if (dbOrder == null){
                //同步落表
                AsrOrderInfoDO orderInfoDO = new AsrOrderInfoDO();
                orderInfoDO.setEnv(env);
                orderInfoDO.setOrderSerialNo(userOrder.getOrderNo());
                orderInfoDO.setMobileNo(userOrder.getMobileNo());
                if (StringUtils.isNotBlank(userOrder.getPassengerOnCarTime())){
                    orderInfoDO.setTripStartTime(DateUtil.string2Date(userOrder.getPassengerOnCarTime()));
                }
                if (StringUtils.isNotBlank(userOrder.getPassengerArriveTime())){
                    orderInfoDO.setTripEndTime(DateUtil.string2Date(userOrder.getPassengerArriveTime()));
                }
                orderInfoDO.setPlateNo(userOrder.getCarNo());
                orderInfoDO.setCreateBy("系统自动同步");
                orderInfoDO.setUpdateBy("系统自动同步");
                orderInfoDO.setCreateTime(new Date());
                orderInfoDO.setUpdateTime(new Date());
                orderInfoDO.setDriverName(userOrder.getDriverName());
                orderInfoDO.setPlateNo(userOrder.getCarNo());
                orderInfoDO.setOrderType(userOrder.getOrderType());
                asrOrderInfoDao.getBaseMapper().insert(orderInfoDO);
            }
        }
    }

    private static String getFileName(String url) {
        if (StringUtils.isBlank(url)){
            return "";
        }
        url = url.split("\\?")[0];
        url = URLDecoder.decode(url, Charset.defaultCharset());
        String[] split = url.split("/");
        return split[split.length - 1];
    }
}
