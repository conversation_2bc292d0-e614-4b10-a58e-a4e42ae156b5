package com.ly.travel.shared.mobility.biz.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务类型枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/3/15 17:21
 */
@Getter
public enum ProductTypeEnum {

    /**
     * 网约车*
     */
    SPECIAL_CAR(11, "预约专车", ProductTypeGroupEnum.ONLINE_SPECIAL_CAR.getValue()),
    ACTUAL_TIME_SPECIAL_CAR(19, "即时专车", ProductTypeGroupEnum.ONLINE_SPECIAL_CAR.getValue()),


    /**
     * 接送机 接送站*
     */
    PICKUP_AIRPORT(12, "接机", ProductTypeGroupEnum.AIRPORT_AND_STATION.getValue()),
    DROPOFF_AIRPORT(13, "送机", ProductTypeGroupEnum.AIRPORT_AND_STATION.getValue()),
    PICKUP_STATION(14, "接站", ProductTypeGroupEnum.AIRPORT_AND_STATION.getValue()),
    DROPOFF_STATION(15, "送站", ProductTypeGroupEnum.AIRPORT_AND_STATION.getValue()),

    /**
     * 顺风车*
     */
    SFC(80, "顺风车", ProductTypeGroupEnum.SFC.getValue()),

    SFC_ALL(90, "上门接送", ProductTypeGroupEnum.SFC.getValue()),
    SFC_PICKUP_AIRPORT(91, "上门接送-接机", ProductTypeGroupEnum.SFC.getValue()),
    SFC_DROP_OFF_AIRPORT(92, "上门接送-送机", ProductTypeGroupEnum.SFC.getValue()),
    SFC_PICK_STATION(93, "上门接送-接站", ProductTypeGroupEnum.SFC.getValue()),
    SFC_DROP_OFF_STATION(94, "上门接送-送站", ProductTypeGroupEnum.SFC.getValue()),
    /**
     * 国际接送机 接送站*
     */
    INTERNATIONAL_PICKUP_AIRPORT(32, "国际接机", ProductTypeGroupEnum.AIRPORT_AND_STATION.getValue()),
    INTERNATIONAL_DROP_OFF_AIRPORT(33, "国际送机", ProductTypeGroupEnum.AIRPORT_AND_STATION.getValue()),
    ;

    private final Integer value;

    private final String desc;

    private final Integer productTypeGroup;

    @Getter
    private static final Map<Integer, ProductTypeEnum> valueMap = new HashMap<>();
    private static final Map<Integer, Set<Integer>> GROUP_VALUE_MAP = new HashMap<>();

    static {
        Arrays.stream(ProductTypeEnum.values()).forEach(e -> valueMap.put(e.getValue(), e));
        for (ProductTypeEnum productTypeEnum : ProductTypeEnum.values()) {
            Set<Integer> productTypeEnums = GROUP_VALUE_MAP.get(productTypeEnum.getProductTypeGroup());
            if (productTypeEnums == null) {
                GROUP_VALUE_MAP.put(productTypeEnum.getProductTypeGroup(), Sets.newHashSet(productTypeEnum.getValue()));
            } else {
                productTypeEnums.add(productTypeEnum.getValue());
            }
        }
    }

    ProductTypeEnum(Integer value, String desc, Integer productTypeGroup) {
        this.value = value;
        this.desc = desc;
        this.productTypeGroup = productTypeGroup;
    }


    @JsonCreator
    public static ProductTypeEnum fromValue(Integer value) {
        return valueMap.get(value);
    }

    @JsonCreator
    public static Set<Integer> fromGroupValue(Integer productTypeGroup) {
        return GROUP_VALUE_MAP.get(productTypeGroup);
    }

    public static String ofDesc(Integer value) {
        ProductTypeEnum productTypeEnum = valueMap.get(value);
        if (productTypeEnum == null) {
            return "";
        }
        return productTypeEnum.getDesc();
    }

    public static String ofDescCollection(Collection<Integer> value) {
        return Arrays.stream(ProductTypeEnum.values()).filter(e -> value.contains(e.getValue())).map(ProductTypeEnum::getDesc).collect(Collectors.joining(","));
    }

}
