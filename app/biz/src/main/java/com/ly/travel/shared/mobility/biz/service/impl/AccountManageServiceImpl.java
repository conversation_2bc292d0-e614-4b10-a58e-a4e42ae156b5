package com.ly.travel.shared.mobility.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ly.travel.shared.mobility.biz.model.dto.AccountDTO;
import com.ly.travel.shared.mobility.biz.model.req.AccountPageQueryReq;
import com.ly.travel.shared.mobility.biz.model.resp.AccountPageQueryResp;
import com.ly.travel.shared.mobility.biz.service.AccountManageService;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao.AsrAccountDao;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrAccountDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/18
 */
@Service
public class AccountManageServiceImpl implements AccountManageService {

    @Resource
    private AsrAccountDao accountDao;
    @Value("${sof-env}")
    private String env;

    public AccountPageQueryResp queryPage(AccountPageQueryReq req) {

        List<AccountDTO> accountList = getAccountList();
        accountList = accountList.stream().filter(account -> {
            if (StringUtils.isNotBlank(req.getName())){
                if (!account.getName().contains(req.getName())){
                    return false;
                }
            }
            if (StringUtils.isNotBlank(req.getMobileNo())){
                if (!Objects.equals(account.getMobile(), req.getMobileNo())){
                    return false;
                }
            }
            if(req.getStatus() != null){
                return Objects.equals(account.getStatus(), req.getStatus());
            }
            return true;
        }).collect(Collectors.toList());

        AccountPageQueryResp resp = new AccountPageQueryResp();
        resp.setPaginator(new AccountPageQueryResp.Paginator(accountList.size()));
        resp.setData(accountList.stream().skip((long) (req.getPage() - 1) * req.getPageSize()).limit(req.getPageSize()).collect(Collectors.toList()));

        return resp;
    }

    @Override
    public void add(AccountDTO req, String operator) {
        if (StringUtils.isAnyBlank(req.getMobile(),req.getPassword())){
            throw new RuntimeException("参数有误!");
        }
        LambdaQueryWrapper<AsrAccountDO> wrapper = Wrappers.<AsrAccountDO>lambdaQuery().eq(AsrAccountDO::getEnv, env);
        wrapper.eq(AsrAccountDO::getMobile, req.getMobile());
        List<AsrAccountDO> dbList = accountDao.getBaseMapper().selectList(wrapper);
        if (CollectionUtils.isNotEmpty(dbList)){
            throw new RuntimeException("手机号已存在!");
        }
        AsrAccountDO account = new AsrAccountDO();
        account.setMobile(req.getMobile());
        account.setPassword(req.getPassword());
        account.setName(req.getName());
        account.setMailAddress(req.getMailAddress());
        account.setMailName(req.getMailName());
        account.setMailPhone(req.getMailPhone());
        account.setStatus(req.getStatus());
        account.setCollectType(req.getCollectType());
        account.setPaymentMethod(req.getPaymentMethod());
        account.setPaymentCardNo(req.getPaymentCardNo());
        account.setPaymentName(req.getPaymentName());
        account.setCreateBy(operator);
        account.setCreateTime(new Date());
        account.setUpdateTime(new Date());
        account.setUpdateBy(operator);
        account.setEnv(env);
        accountDao.getBaseMapper().insert(account);
    }

    public void update(AccountDTO req,String operator) {

        AsrAccountDO targetAccount = accountDao.getBaseMapper().selectById(req.getId());

        if (targetAccount == null){
            throw new RuntimeException("账号不存在!");
        }
        targetAccount.setPassword(req.getPassword());
        targetAccount.setName(req.getName());
        targetAccount.setMailAddress(req.getMailAddress());
        targetAccount.setMailName(req.getMailName());
        targetAccount.setMailPhone(req.getMailPhone());
        targetAccount.setStatus(req.getStatus());
        targetAccount.setCollectType(req.getCollectType());
        targetAccount.setPaymentMethod(req.getPaymentMethod());
        targetAccount.setPaymentCardNo(req.getPaymentCardNo());
        targetAccount.setPaymentName(req.getPaymentName());
        targetAccount.setUpdateBy(operator);
        targetAccount.setUpdateTime(new Date());
        accountDao.getBaseMapper().updateById(targetAccount);
    }

    public List<AccountDTO> getAccountList() {

        LambdaQueryWrapper<AsrAccountDO> wrapper = Wrappers.<AsrAccountDO>lambdaQuery().eq(AsrAccountDO::getEnv, env);
        List<AsrAccountDO> dbList = accountDao.getBaseMapper().selectList(wrapper);
        return dbList.stream().map(e -> {
            AccountDTO account = new AccountDTO();
            BeanUtils.copyProperties(e, account);
            account.setUserAccount(e.getMobile());
            return account;
        }).collect(Collectors.toList());
    }
}
