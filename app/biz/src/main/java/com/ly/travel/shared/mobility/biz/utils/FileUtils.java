package com.ly.travel.shared.mobility.biz.utils;

import com.github.junrar.Archive;
import com.github.junrar.rarfile.FileHeader;
import com.ly.travel.shared.mobility.biz.model.vo.meta.FileMetaInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description: 文件处理
 * @Author: jay.he
 * @Date: 2025-08-18 15:49
 * @Version: 1.0
 **/
@Slf4j
public class FileUtils {

    /**
     * 解压文件并获取音视频文件的元数据信息
     *
     * @param zipFilePath 压缩文件路径
     * @return 音视频文件元数据列表
     */
    public static List<FileMetaInfo> extractAndListMediaFiles(String zipFilePath) {
        List<FileMetaInfo> fileList = new ArrayList<>();
        // 创建解压目录
        String extractDir = zipFilePath.substring(0, zipFilePath.lastIndexOf(".")) + "_extracted/";
        new File(extractDir).mkdirs();

        try {
            if (zipFilePath.toLowerCase().endsWith(".zip")) {
                // 解压ZIP文件
                unzipMediaFiles(zipFilePath, extractDir);
            } else if (zipFilePath.toLowerCase().endsWith(".rar")) {
                // 解压RAR文件
                unrarMediaFiles(zipFilePath, extractDir);
            }

            // 遍历解压后的目录，获取所有音视频文件（包括嵌套目录中的文件）
            scanMediaFilesInDirectory(new File(extractDir), fileList);
        } catch (Exception e) {
            log.error("Error extracting and listing media files: {}", e);
        }
        return fileList.stream()
                .filter(fileMetaInfo -> !fileMetaInfo.getFileName().startsWith("."))
                .collect(Collectors.toList());
    }

    /**
     * 解压ZIP文件
     */
    public static void unzipMediaFiles(String zipFilePath, String extractDir) throws IOException {
        try (ZipFile zipFile = new ZipFile(new File(zipFilePath))) {
            Enumeration<ZipArchiveEntry> entries = zipFile.getEntries();

            while (entries.hasMoreElements()) {
                ZipArchiveEntry entry = entries.nextElement();
                String entryName = entry.getName();
                File destFile = new File(extractDir, entryName);

                if (entry.isDirectory()) {
                    destFile.mkdirs();
                } else {
                    // 确保父目录存在
                    destFile.getParentFile().mkdirs();

                    // 解压文件
                    try (InputStream is = zipFile.getInputStream(entry);
                         FileOutputStream fos = new FileOutputStream(destFile)) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = is.read(buffer)) > 0) {
                            fos.write(buffer, 0, length);
                        }
                    }

                    // 设置文件的修改时间为压缩包中的时间
                    destFile.setLastModified(entry.getTime());
                }
            }
        }
    }

    /**
     * 解压RAR文件
     */
    public static void unrarMediaFiles(String rarFilePath, String extractDir) throws Exception {
        try (Archive archive = new Archive(new File(rarFilePath))) {
            FileHeader fileHeader;
            while ((fileHeader = archive.nextFileHeader()) != null) {
                String fileName = fileHeader.getFileNameString().trim();
                File destFile = new File(extractDir, fileName);

                if (fileHeader.isDirectory()) {
                    destFile.mkdirs();
                } else {
                    // 确保父目录存在
                    destFile.getParentFile().mkdirs();

                    // 解压文件
                    try (FileOutputStream fos = new FileOutputStream(destFile)) {
                        archive.extractFile(fileHeader, fos);
                    }

                    // 设置文件的修改时间
                    destFile.setLastModified(fileHeader.getMTime().getTime());
                }
            }
        }
    }

    /**
     * 扫描目录中的媒体文件并获取元信息
     */
    public static void scanMediaFilesInDirectory(File directory, List<FileMetaInfo> fileList) {
        // 定义音视频文件扩展名
        Set<String> mediaExtensions = new HashSet<>(Arrays.asList(
                "mp3", "wav", "flac", "aac", "m4a", "wma", "ogg",  // 音频格式
                "mp4", "avi", "mov", "wmv", "flv", "mkv", "webm"   // 视频格式
        ));

        scanDirectory(directory, mediaExtensions, fileList);
    }

    /**
     * 递归扫描目录
     */
    public static void scanDirectory(File directory, Set<String> mediaExtensions, List<FileMetaInfo> fileList) {
        File[] files = directory.listFiles();
        if (files == null) return;

        for (File file : files) {
            if (file.isDirectory()) {
                // 递归扫描子目录
                scanDirectory(file, mediaExtensions, fileList);
            } else {
                String fileName = file.getName();
                String extension = getFileExtension(fileName).toLowerCase();

                if (mediaExtensions.contains(extension)) {
                    try {
                        FileMetaInfo metadata = getFileMetadata(file);
                        fileList.add(metadata);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 获取文件元信息
     */
    public static FileMetaInfo getFileMetadata(File file) throws IOException {
        Path path = file.toPath();
        BasicFileAttributes attrs = Files.readAttributes(path, BasicFileAttributes.class);

        return FileMetaInfo.builder()
                .fileName(file.getName())
                .filePath(file.getAbsolutePath())
                .fileSize(attrs.size())
                .fileSizeStr(formatFileSize(attrs.size()))
                .createTime(new Date(attrs.creationTime().toMillis()))
                .modifyTime(new Date(attrs.lastModifiedTime().toMillis()))
                .extension(getFileExtension(file.getName()))
                .isReadable(file.canRead())
                .build();
    }

    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long size) {
        if (size <= 0) return "0 B";

        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));

        return String.format("%.2f %s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }

    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String fileName) {
        int lastIndexOf = fileName.lastIndexOf(".");
        if (lastIndexOf == -1) {
            return "";
        }
        return fileName.substring(lastIndexOf + 1);
    }

    /**
     * 异步删除文件
     *
     * @param filePath 文件路径
     * @return CompletableFuture
     */
    public static CompletableFuture<Boolean> delFileAsync(String filePath) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                delFile(filePath);
                return true;
            } catch (Exception e) {
                log.error("异步删除文件失败: {}", filePath, e);
                return false;
            }
        });
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     * @return
     */
    public static boolean delFile(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        try {
            Path path = Paths.get(filePath);
            Files.deleteIfExists(path); // 如果文件存在则删除
            log.info("文件删除成功: {}", filePath);
            return true;
        } catch (IOException e) {
            log.error("删除文件失败: {}", filePath, e);
            throw new RuntimeException("删除文件失败", e);
        }
    }
}
