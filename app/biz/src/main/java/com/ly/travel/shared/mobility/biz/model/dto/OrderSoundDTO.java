package com.ly.travel.shared.mobility.biz.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/19
 */
@Data
public class OrderSoundDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单流水号
     */
    private String orderSerialNo;

    private String orderTypeDesc;

    /**
     * 手机号
     */
    private String mobileNo;

    /**
     * 行程开始时间
     */
    private Date tripStartTime;

    /**
     * 行程结束时间
     */
    private Date tripEndTime;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 上传状态
     */
    private Integer uploadStatus;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 审核意见
     */
    private String auditComment;

    /**
     * 司机是否违规 0:否 1:是
     */
    private Integer driverViolated;

    /**
     * 上传文件地址
     */
    private String fileUrl;

    private String fileName;

    /**
     * 操作人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 司机服务评分
     */
    private BigDecimal driverScore;
    /**
     * 检查员司机服务标签
     */
    private String serviceTag;
    /**
     * 检查员对司机点评
     */
    private String serviceReview;
    /**
     * 检查员对司机评分
     */
    private BigDecimal ratingScore;
}
