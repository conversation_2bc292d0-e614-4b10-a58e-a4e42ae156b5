package com.ly.travel.shared.mobility.biz.service;

import com.ly.travel.shared.mobility.biz.model.dto.AccountDTO;
import com.ly.travel.shared.mobility.biz.model.req.AccountPageQueryReq;
import com.ly.travel.shared.mobility.biz.model.resp.AccountPageQueryResp;

import java.util.List;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/19
 */
public interface AccountManageService {

    AccountPageQueryResp queryPage(AccountPageQueryReq req);

    void add(AccountDTO req, String operator);

    void update(AccountDTO req, String operator);

    List<AccountDTO> getAccountList();
}
