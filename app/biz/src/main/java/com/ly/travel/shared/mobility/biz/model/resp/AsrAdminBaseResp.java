package com.ly.travel.shared.mobility.biz.model.resp;

import com.ly.travel.shared.mobility.integration.enums.AsrAdminErrorEnum;
import lombok.Data;

@Data
public class AsrAdminBaseResp<T> {
    /**
     * 错误吗
     */
    private int errCode;
    /**
     * 消息
     */
    private String msg;
    /*

    数据
     */
    private T data;

    public static <T> AsrAdminBaseResp ok(T data) {
        AsrAdminBaseResp resp = new AsrAdminBaseResp();
        resp.setData(data);
        resp.setErrCode(AsrAdminErrorEnum.SUCCESS.getCode());
        resp.setMsg(AsrAdminErrorEnum.SUCCESS.getDesc());
        return resp;
    }

    public static <T> AsrAdminBaseResp ok() {
        AsrAdminBaseResp resp = new AsrAdminBaseResp();
        resp.setErrCode(AsrAdminErrorEnum.SUCCESS.getCode());
        resp.setMsg(AsrAdminErrorEnum.SUCCESS.getDesc());
        return resp;
    }

    public static <T> AsrAdminBaseResp fail(int code, String msg) {
        AsrAdminBaseResp resp = new AsrAdminBaseResp();
        resp.setErrCode(code);
        resp.setMsg(msg);
        return resp;
    }

    public static <T> AsrAdminBaseResp fail(AsrAdminErrorEnum errorEnum) {
        AsrAdminBaseResp resp = new AsrAdminBaseResp();
        resp.setErrCode(errorEnum.getCode());
        resp.setMsg(errorEnum.getDesc());
        return resp;
    }

    public static <T> AsrAdminBaseResp fail(String message) {
        AsrAdminBaseResp resp = new AsrAdminBaseResp();
        resp.setErrCode(AsrAdminErrorEnum.SYSTEM_EXCEPTION.getCode());
        resp.setMsg(message);
        return resp;
    }

    public boolean isSuccess() {
        return AsrAdminErrorEnum.SUCCESS.getCode().equals(this.errCode);
    }

}
