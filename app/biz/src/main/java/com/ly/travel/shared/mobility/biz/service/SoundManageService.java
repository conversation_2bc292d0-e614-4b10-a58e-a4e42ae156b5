package com.ly.travel.shared.mobility.biz.service;

import com.ly.travel.shared.mobility.biz.model.dto.OrderSoundAuditDTO;
import com.ly.travel.shared.mobility.biz.model.dto.OrderSoundDTO;
import com.ly.travel.shared.mobility.biz.model.req.SoundPageQueryReq;
import com.ly.travel.shared.mobility.biz.model.req.SoundTextQueryReq;
import com.ly.travel.shared.mobility.biz.model.resp.OrderSoundToTextResp;
import com.ly.travel.shared.mobility.biz.model.resp.SoundPageQueryResp;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/19
 */
public interface SoundManageService {
    SoundPageQueryResp queryPage(SoundPageQueryReq req);

    void audit(OrderSoundAuditDTO req, String user);

    OrderSoundToTextResp querySoundToText(SoundTextQueryReq req);
}
