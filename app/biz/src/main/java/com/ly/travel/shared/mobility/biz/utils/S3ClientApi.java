package com.ly.travel.shared.mobility.biz.utils;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Decoder;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR> By Houce
 * @since 2023/2/14
 */
@Component
@Slf4j
public class S3ClientApi {

    private AmazonS3Client s3client;

    private static final String BUCKET_NAME = "bucket-asr-core";
    /**
     * 私有访问地址域名
     */
    private static final String PRIVATE_URL = "oss.dss.17usoft.com";
    /**
     * 公有访问地址域名
     */
    private static final String PUBLIC_URL = "file.40017.cn";



    public AmazonS3Client getS3client(){

        if (s3client == null) {

            String accessKey = "kNjwL0xuM7El6V7Jubrn";
            String secretKey = "NjEmhg85apY4veef4qcsfrMn1gkKP3Cbcfq5sY1d";
            String endpoint = "http://oss.dss.17usoft.com";

            AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
            ClientConfiguration clientCfg = new ClientConfiguration();
            clientCfg.setProtocol(endpoint.startsWith(Protocol.HTTPS.toString()) ? Protocol.HTTPS : Protocol.HTTP);
            s3client = new AmazonS3Client(credentials, clientCfg);
            s3client.setEndpoint(endpoint);
            s3client.setS3ClientOptions(new S3ClientOptions().withPathStyleAccess(true));
        }
        return s3client;
    }

    public InputStream base64ToInputStream(String base64string) throws IOException {
        BASE64Decoder decoder = new BASE64Decoder();
        byte[] bytes = decoder.decodeBuffer(base64string);
        return new ByteArrayInputStream(bytes);
    }

    /**
     * 上传文件(流格式)
     * 文档地址:http://efs.inf.17usoft.com/docs/s3proxy/s3proxy-usejavasdk
     * @param fileName 文件名称
     * @param input 文件流
     */
    public void putObjByStream(String fileName, InputStream input){

        AmazonS3Client s3client = getS3client();
        ObjectMetadata meta = new ObjectMetadata();
        try {
            meta.setContentLength(input.available());
        } catch (IOException e) {
            log.error("获取流字节长度异常",e);
        }
        PutObjectRequest request = new PutObjectRequest(BUCKET_NAME, fileName, input,meta);
        s3client.putObject(request);
    }

    /**
     * 获取文件
     * @param fileName 文件名
     * @return 文件对象
     */
    public S3Object getFile(String fileName) {
        AmazonS3Client s3client = getS3client();
        GetObjectRequest request = new GetObjectRequest(BUCKET_NAME, fileName);
        S3Object object = s3client.getObject(request);
        return object;
    }

    /**
     * 根据key产生完整的url
     *
     * @param fileName 文件名
     * @return
     * 强烈建议使用生成公有地址（公有地址的url不变，这样ceph后端可以有缓存作用，也不会穿透CDN），生成公有地址方法点击链接：http://wiki.17usoft.com/pages/viewpage.action?pageId=15040422
     */
    public String getFileUrl(String fileName) {
        AmazonS3Client s3client = getS3client();
        URL url = s3client.getUrl(BUCKET_NAME,fileName);
        String prepareUrl = url.toString();
        return prepareUrl.replace(PRIVATE_URL,PUBLIC_URL);
    }

    /**
     * 根据bucket与key 产生完整的url
     *
     * @param conn
     * @param bucket
     * @param key
     * @return
     * 强烈建议使用生成公有地址（公有地址的url不变，这样ceph后端可以有缓存作用，也不会穿透CDN），生成公有地址方法点击链接：http://wiki.17usoft.com/pages/viewpage.action?pageId=15040422
     */
    public String getURL(String bucket, String key,AmazonS3Client s3Client) {
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucket, key);
        Date expirationDate = null;
        try {
            expirationDate = new SimpleDateFormat("yyyy-MM-dd").parse("2029-12-31");
        } catch (Exception e) {
            e.printStackTrace();
        }
        request.setExpiration(expirationDate);
        URL url = s3Client.generatePresignedUrl(request);
        return url == null ? "none" : url.toString();
    }

}
