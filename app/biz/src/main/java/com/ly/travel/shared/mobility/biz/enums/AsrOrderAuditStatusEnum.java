package com.ly.travel.shared.mobility.biz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum AsrOrderAuditStatusEnum {

    WAIT_AUDIT(0,"待审核"),
    HAD_AUDIT(1,"已审核"),
    ;

    private final Integer status;

    private final String desc;

    public static String getDescByCode(Integer code){
        AsrOrderAuditStatusEnum typeEnum = Arrays.stream(values()).filter(s -> s.getStatus().equals(code)).findFirst().orElse(null);
        if(typeEnum !=null){
            return typeEnum.desc;
        }
        return StringUtils.EMPTY;
    }
}
