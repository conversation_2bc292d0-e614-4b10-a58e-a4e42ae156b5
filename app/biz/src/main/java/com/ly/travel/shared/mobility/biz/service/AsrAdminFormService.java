package com.ly.travel.shared.mobility.biz.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.flight.toolkit.ContentDifferWrapper;
import com.ly.flight.toolkit.ObjectDiffer;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageQuery;
import com.ly.sof.utils.page.PageUtils;
import com.ly.travel.shared.mobility.biz.model.dto.BaseFieldDTO;
import com.ly.travel.shared.mobility.dal.mybatisplus.utils.UserInfoUtils;
import com.ly.travel.shared.mobility.integration.client.orderservice.OrderServiceClient;
import com.ly.travel.shared.mobility.integration.enums.LogTypeEnum;
import com.ly.travel.shared.mobility.integration.exception.AsrAdminException;
import com.ly.travel.shared.mobility.integration.exception.AsrAdminWarnException;
import com.ly.travel.shared.mobility.integration.utils.DeepCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Slf4j
public abstract class AsrAdminFormService<TReq extends PageQuery, TRespAndSaveDTO extends BaseFieldDTO, TDO> {
    @Resource
    private OrderServiceClient orderServiceClient;

    /**
     * 记录日志的类型
     *
     * @return
     */
    public LogTypeEnum logTypeEnum() {
        throw new AsrAdminException("没有实现logTypeEnum方法");
    }

    /**
     * 根据id 查询db 数据
     *
     * @param id
     * @return
     */
    public TDO queryDOById(Long id) {
        throw new AsrAdminException("没有实现queryDOById方法");
    }

    /**
     * 新增或更新动作
     *
     * @param req
     * @return
     */
    public Long save(TRespAndSaveDTO req) {
        throw new AsrAdminException("没有实现save方法");
    }

    /**
     * 分页查询db数据
     *
     * @param req
     * @return
     */
    public abstract Page<TDO> queryDBPage(TReq req);

    /**
     * 把数据库do 转成页面 resp
     *
     * @param doList
     * @return
     */
    public abstract List<TRespAndSaveDTO> covertToResp(List<TDO> doList);


    /**
     * 批量保存数据并记录操作日志
     *
     */
    public void batchSaveWithOperatorLog(List<TRespAndSaveDTO> batchDTOList) {
        if (CollectionUtils.isEmpty(batchDTOList)) {
            return;
        }
        //循环操作
        for (TRespAndSaveDTO saveDTO : batchDTOList) {
            saveWithOperateLog(saveDTO,true);
        }
    }


    /**
     * 根据请求参数查询分页结果
     *
     * @return 分页结果
     */
    public PageList<TRespAndSaveDTO> queryPage(TReq req) {
        Page<TDO> page = queryDBPage(req);
        List<TRespAndSaveDTO> respList = covertToResp(page.getRecords());
        PageList<TRespAndSaveDTO> pageList = PageUtils.createPageList(respList, req, (int) page.getTotal());
        return pageList;
    }

    /**
     * 保存操作并生成操作日志
     *
     */
    public void saveWithOperateLog(TRespAndSaveDTO req) {
        saveWithOperateLog(req,false);
    }
    /**
     * 保存操作并生成操作日志
     *
     */
    private void saveWithOperateLog(TRespAndSaveDTO req,boolean batchOperate) {
        TRespAndSaveDTO oldResp = null;
        String logTypeDesc = "<span style='color:green;'>新增</span>";
        if (req.getId() != null) {
            TDO tdo = queryDOById(req.getId());
            List<TRespAndSaveDTO> tRespList = covertToResp(Arrays.asList(tdo));
            if (CollectionUtils.isNotEmpty(tRespList)) {
                oldResp = tRespList.get(0);
            }
            //批量操作
            if(batchOperate){
                logTypeDesc = "<span style='color:blue;'>批量操作</span>";
            }else {
                logTypeDesc = "<span style='color:red;'>修改</span>";
            }
        }

        ContentDifferWrapper differ = new ContentDifferWrapper(ObjectDiffer.getInstance());
        TRespAndSaveDTO compareResp = DeepCopyUtils.mergeObjects(req, oldResp);
        String diffLog = differ.diffVueHtml(oldResp, compareResp);

        //批量更新的话,不需要提示这个,因为会中断
        if (StringUtils.isBlank(diffLog) && !batchOperate) {
            throw new AsrAdminWarnException("本次操作无变化,无需更新");
        }
        //保存数据库
        Long id = save(req);
        //记录日志
        if(StringUtils.isNotBlank(diffLog)) {
            String tarDiffLog = logTypeDesc + diffLog;
            orderServiceClient.saveCommonLog(String.valueOf(id), tarDiffLog, UserInfoUtils.getUser(), logTypeEnum());
        }
    }

}
