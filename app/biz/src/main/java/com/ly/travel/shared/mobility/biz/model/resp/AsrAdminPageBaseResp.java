package com.ly.travel.shared.mobility.biz.model.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/18
 */
@Data
public class AsrAdminPageBaseResp<T> {

    private List<T> data;

    private Paginator paginator;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Paginator {

        private long totalCount;
    }
}
