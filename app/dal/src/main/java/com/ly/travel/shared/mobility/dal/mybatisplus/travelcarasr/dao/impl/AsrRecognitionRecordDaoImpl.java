package com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao.impl;

import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrRecognitionRecordDO;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.mapper.AsrRecognitionRecordMapper;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao.AsrRecognitionRecordDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 语音识别结果记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/19 19:52
 */
@Service
public class AsrRecognitionRecordDaoImpl extends ServiceImpl<AsrRecognitionRecordMapper, AsrRecognitionRecordDO> implements AsrRecognitionRecordDao {

}
