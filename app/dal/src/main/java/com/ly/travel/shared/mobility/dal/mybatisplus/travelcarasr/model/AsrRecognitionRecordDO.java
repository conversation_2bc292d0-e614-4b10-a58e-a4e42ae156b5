package com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 语音识别结果记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/19 19:52
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("asr_recognition_record")
public class AsrRecognitionRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 识别音视频文件的元数据信息
     */
    @TableField("meta_info")
    private String metaInfo;

    /**
     * 识别结果
     */
    private String result;

    /**
     * 数据状态0：正常 -1：失效
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 环境
     */
    @TableField(fill = FieldFill.INSERT)
    private String env;

    /**
     * 文件识别序号，按音视频文件原创建时间排序(值越小时间越早)
     */
    @TableField("seq_no")
    private Integer seqNo;

    /**
     * 资源S3地址
     */
    @TableField("resource_url")
    private String resourceUrl;

    /**
     * 合并后的资源S3地址
     */
    @TableField("merge_resource_url")
    private String mergeResourceUrl;
}
