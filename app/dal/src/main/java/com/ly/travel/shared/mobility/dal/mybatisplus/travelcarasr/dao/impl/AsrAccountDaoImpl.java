package com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao.impl;

import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrAccountDO;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.mapper.AsrAccountMapper;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao.AsrAccountDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 语音识别结果记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/25 14:58
 */
@Service
public class AsrAccountDaoImpl extends ServiceImpl<AsrAccountMapper, AsrAccountDO> implements AsrAccountDao {

}
