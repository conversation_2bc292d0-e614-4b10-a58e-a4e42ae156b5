package com.ly.travel.shared.mobility.dal.mybatisplus.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum StatusEnum {
    DELETE(-1, "删除"),
    INVALID(0, "无效"),
    VALID(1, "有效"),
    ;

    private Integer code;
    private String desc;

    public static String getDescByCode(Integer code){
        if(Objects.isNull(code)){
            return StringUtils.EMPTY;
        }
        for(StatusEnum type : values()){
            if(type.getCode().equals(code)){
                return type.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }
}
