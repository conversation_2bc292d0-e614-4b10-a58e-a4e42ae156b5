package com.ly.travel.shared.mobility.dal.mybatisplus.utils;


import com.alibaba.fastjson.JSON;
import com.ly.travel.shared.mobility.supply.authentication.model.UserInfo;
import com.ly.travel.shared.mobility.supply.authentication.repository.UserInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Slf4j
public class UserInfoUtils {
    public static String getUser() {
        UserInfo userInfo = UserInfoRepository.getUserInfo();
        if (userInfo == null) {
            return "test";
        }
        return userInfo.getUserName() + " " + userInfo.getAccount();
    }

    public static UserInfo getUserInfo() {
        UserInfo userInfo = UserInfoRepository.getUserInfo();
        if (userInfo == null) {
            return new UserInfo(0L, "test", "test", 0, "0");
        }
        return userInfo;
    }

    public static List<String> getPermissionList() {
        UserInfo userInfo = getUserInfo();
        log.info("当前用户信息:{}", JSON.toJSONString(userInfo));
        if (userInfo.getUserName().equals("test")) {
            return Arrays.asList("*");
        }
        if (StringUtils.isBlank(userInfo.getPermission())) {
            return new ArrayList<>();
        }
        List<String> list = Arrays.asList(userInfo.getPermission().split("/"));
        return list;
    }

}
