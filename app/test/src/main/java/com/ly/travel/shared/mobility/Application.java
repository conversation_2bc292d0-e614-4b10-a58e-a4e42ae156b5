package com.ly.travel.shared.mobility;

import com.ly.flight.toolkit.ApplicationLauncher;
import com.ly.flight.toolkit.config.Configure;

/**
 * 应用启动类
 *
 * @wiki http://dev.travel.17usoft.com/tools/10SOF-APP%E5%BF%AB%E9%80%9F%E5%90%AF%E5%8A%A8%E5%99%A8.html#_2-2-%E6%B7%BB%E5%8A%A0%E5%90%AF%E5%8A%A8%E7%B1%BB
 * <AUTHOR> 1011853)
 * @version Id : Application, v 0.1 2023/12/5 14:28 huangxin Exp $
 */
public class Application {

    public static void main(String[] args) {
        Configure configure = new Configure();
        configure.setEnv("dev");
        // 1、当前项目应用标志
        configure.setAppUk("groundtravel.shared.mobility.asr.admin");
        // 2、当前项目启动端口
        configure.setPort(8080);
        // 3、【只留其一】当前项目是WEB项目
        configure.setProjectType(Configure.SofProjectType.WEB);
        // 4、【DSF】是否挂载DSF服务
        configure.setStartDsf(false);
        // 4、是否使用本地的配置文件做替换，比如在有remote-config之前，都是在filter目录下配置的各环境的配置文件值
        // 接入远程配置后改为false，默认false
        configure.useLocalConfig(true);
        // 5、【WEB】当前WEB应用启动路径
        configure.setWebPath("/carAsrAdmin");
        configure.setWebServletXmlPath("WEB-INF/sofweb-servlet.xml");
        // 替换本地启动目录的日志根目录
        configure.addMockConfigKV("log4j2.xml", "log.root", Application.class.getClassLoader().getResource("").getPath());

        ApplicationLauncher launcher = new ApplicationLauncher();
        launcher.start(configure);
    }
}
