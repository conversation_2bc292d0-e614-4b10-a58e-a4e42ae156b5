<template>
    <div>
        <el-select class="w100" v-model="sonValue" :multiple="isMultiple" filterable clearable :placeholder="text"
                   :disabled="isDisabled"
                   :loading="loading" @change="changeSelect">
            <el-option :label="formatLabel(item)"
                       :value="formatValue(item)"
                       :key="item.id"
                       v-for="item in options">
            </el-option>
        </el-select>
    </div>
</template>
<script>


import {isEmpty} from "@/utils/util";

export default {
    props: {
        value: {
            type: [String, Number, Array],
            default: ''
        },
        text: {
            type: String,
            default: '请选择'
        },
        isDisabled: {
            type: Boolean,
            default: false
        },
        storeData: {
            type: Object,
            default: null
        },
        isMultiple: {
            type: Boolean,
            default: false
        },
    },
    model: {
        prop: 'value',
        event: 'valueChange'
    },
    data() {
        return {
            options: [],
            sonValue: this.value,
            loading: false
        }
    },
    watch: {
        value(newVal, oldVal) {
            this.sonValue = newVal
        }
    },
    created() {
        this.searchData()

    },
    methods: {
        formatLabel(item) {
            return `${item.tcId}-${item.nameShort}`
        },
        formatValue(item) {
            return item.tcId
        },
        searchData() {

            this.loading = true
            let param = {
                url: `commondata/cityList`,
                method: 'GET',
            }

            this.$http(param).then((res) => {
                console.log(res)
                if (res && !isEmpty(res.data)) {
                    this.options = res.data
                } else {
                    this.options = []
                }
                this.loading = false
            }).catch(err => {
                console.log(err);
                this.options = []
                this.loading = false
            })

        },
        changeSelect(value) {

            if (Array.isArray(value)) {
                let list = this.options.filter(s => value.indexOf(s.tcId) > -1)
                let tarList = []
                if (!isEmpty(list)) {
                    tarList = list.map(s => {
                        return {
                            cityId: s.tcId || '',
                            name: s.pname || '',
                        }
                    })
                }
                this.$emit('callbackchange', tarList)
            } else {
                let obj = this.options.find(son => son.tcId === value) || {}

                this.$emit('callbackchange', {
                    cityId: obj.tcId || '',
                    name: obj.pname || '',
                })
            }
            this.$emit('valueChange', this.sonValue)
        }
    }
}

</script>

