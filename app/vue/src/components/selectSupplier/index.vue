<template>
    <div>
        <el-select class="w100"  collapse-tags ref="sel" reserve-keyword v-model="sonValue" :multiple="isMultiple" :remote="isRemote" filterable clearable :placeholder="text"
                   :disabled="isDisabled"
                   :remote-method="remoteMethod" :loading="loading" @change="changeSelect"
                   @focusout.native="input">
            <el-row v-if="isMultiple">
                <el-col :span="12">
                    <el-tag type="primary" class="w100" style="text-align: center;cursor: pointer"
                            @click="selectAllClick">
                        全选列表值
                    </el-tag>
                </el-col>
                <el-col :span="12">
                    <el-tag type="warning" class="w100" style="text-align: center;cursor: pointer"
                            @click="clearAllClick">
                        清空列表值
                    </el-tag>
                </el-col>
            </el-row>
            <el-option :label="formatLabel(item)"
                       :value="formatValue(item)"
                       :key="item.id"
                       v-for="item in options">
            </el-option>
        </el-select>
    </div>
</template>
<script>


import {isEmpty} from "@/utils/util";

export default {
    props: {
        value: {
            type: [String, Number, Array],
            default: ''
        },
        text: {
            type: String,
            default: '请输入关键字'
        },
        isDisabled: {
            type: Boolean,
            default: false
        },
        storeData: {
            type: Object,
            default: null
        },
        isMultiple: {
            type: Boolean,
            default: false
        },
        isRemote: {
            type: Boolean,
            default: true
        },
    },
    model: {
        prop: 'value',
        event: 'valueChange'
    },
    data() {
        return {
            options: [],
            allOptions: [],
            sonValue: this.value,
            loading: false,
            tagLabel:''
        }
    },
    watch: {
        value(newVal, oldVal) {
            this.sonValue = newVal
        }
    },
    created() {
        let val = this.isRemote ? this.value : ''
        this.remoteMethod(val)
    },
    methods: {
        input(){
            if(this.$refs.sel && this.$refs.sel.$refs.input){
                this.tagLabel = this.$refs.sel.$refs.input.value
            }else {
                this.tagLabel = ''
            }
        },
        selectAllClick() {
            let optValList = this.options.filter(s => this.formatLabel(s).toLowerCase().includes(this.tagLabel.toLowerCase())).map(s => s.supplierCode).filter(s => !this.sonValue.includes(s))
            this.sonValue.push(...optValList)
            this.$emit('valueChange', this.sonValue)
            this.changeSelect(this.sonValue)
        },
        clearAllClick() {
            let optValList = this.options.filter(s => this.formatLabel(s).toLowerCase().includes(this.tagLabel.toLowerCase())).map(s => s.supplierCode)
            this.sonValue = this.sonValue.filter(s => !optValList.includes(s))
            this.$emit('valueChange', this.sonValue)
            this.changeSelect(this.sonValue)
        },
        formatLabel(item) {
            return `${item.supplierName}(${item.supplierCode})`
        },
        formatValue(item) {
            return item.supplierCode
        },
        remoteMethod(value) {
            if(this.isRemote && isEmpty(value)){
                this.options = []
                return
            }

            this.loading = true

            let keyword = value
            if(Array.isArray(value)){
                keyword = value.join(',')
            }
            const data = new FormData();
            data.append("keyword",value)
            let param = {
                url: `commondata/supplierList`,
                method: 'POST',
                data
            }

            this.$http(param).then((res) => {
                console.log(res)
                if (res && !isEmpty(res.data)) {
                    this.options = res.data
                    let allSupplierCodes =  this.allOptions.map(s=>s.supplierCode)
                    let noInOpts = this.options.filter(s=>!allSupplierCodes.includes(s.supplierCode))
                    this.allOptions.push(...noInOpts)
                } else {
                    this.options = []
                }
                this.loading = false
            }).catch(err => {
                console.log(err);
                this.options = []
                this.loading = false
            })
        },
        changeSelect(value) {
            if (Array.isArray(value)) {
                let list = this.allOptions.filter(s => value.indexOf(s.supplierCode) > -1)
                let tarList = []
                if (!isEmpty(list)) {
                    tarList = list.map(s => {
                        return {
                            name: s.supplierName || '',
                            code: s.supplierCode || '',
                        }
                    })
                }
                this.$emit('callbackchange', tarList)
            } else {
                let obj = this.allOptions.find(son => son.supplierCode === value) || {}

                this.$emit('callbackchange', {
                    name: obj.supplierName || '',
                    code: obj.supplierCode || '',
                })
            }
            this.$emit('valueChange', this.sonValue)

        }
    }
}

</script>

