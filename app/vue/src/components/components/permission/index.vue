<template>
  <span v-if="hadPermission">
    <slot></slot>
  </span>
</template>

<script>
import {isEmpty, getSession} from '@/utils/util'

export default {
    props: {
        name: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            hadPermission: false
        }
    },
    computed: {},
    created() {
        this.checkPermission()
    },
    methods: {
        checkPermission() {
            let permissionList = getSession('permissionList')
            if (!isEmpty(permissionList)) {
                this.hadPermission = permissionList.indexOf(this.name) > -1 || permissionList.indexOf("*") > -1
                return
            }

            let param = {
                url: `userinfo/queryPermissionList`,
                method: 'POST'
            }

             this.$http(param).then((res) => {
                if (res && res.success) {
                    permissionList = res.data
                    this.hadPermission = permissionList.indexOf(this.name) > -1 || permissionList.indexOf("*") > -1
                }
            }).catch(err => {
                console.log(err)
            })
        },


    }
}
</script>
