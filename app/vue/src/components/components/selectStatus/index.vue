<template>
    <div>
        <el-select class="w100" v-model="sonValue" filterable clearable :placeholder="text" :disabled="isDisabled"
                   :loading="loading" @change="changeSelect">
            <el-option :label="formatLabel(item)"
                       :value="formatValue(item)"
                       :key="item.value"
                       v-for="item in options">
            </el-option>
        </el-select>
    </div>
</template>
<script>


export default {
    props: {
        value: {
            type: [String, Number],
            default: ''
        },
        text: {
            type: String,
            default: '请选择'
        },
        isDisabled: {
            type: Boolean,
            default: false
        },
        storeData: {
            type: Object,
            default: null
        },
    },
    model: {
        prop: 'value',
        event: 'valueChange'
    },
    data() {
        return {
            options: [{value: 1, desc: '有效'}, {value: 0, desc: '无效'}],
            sonValue: this.value,
            loading: false
        }
    },
    watch: {
        value(newVal, oldVal) {
            this.sonValue = newVal
        }
    },
    created() {
    },
    methods: {
        formatLabel(item) {
            return `${item.desc}`
        },
        formatValue(item) {
            return item.value
        },
        changeSelect(value) {
            let obj = this.options.find(son => son.value === value) || {}

            this.$emit('callbackchange', {
                desc: obj.desc || '',
                value: obj.value || '',
            })
            this.$emit('valueChange', this.sonValue)
        }
    }
}


</script>

