<template>
    <el-dialog v-loading="loading" :visible="isShow" title="操作日志"
               :close-on-click-modal="false" @close="cancelClick"
               :append-to-body="true" width="50%" center>
        <el-table :data="dataList" :highlight-current-row="true" border stripe>
            <el-table-column prop="operateUser"  label="操作人"  width="180"></el-table-column>
            <el-table-column label="内容" >
                <template slot-scope="scope">
                    <span v-html="scope.row.operatorValue"></span>
                </template>
            </el-table-column>
            <el-table-column prop="operatorFormatTime" label="记录时间" width="200"></el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
       <el-button type="default" size="mini" @click="cancelClick">
            取消
        </el-button>
    </span>
    </el-dialog>
</template>

<script>

import {isEmpty} from "@/utils/util";

export default {
    props: {
        logType: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            isShow: false,
            dataList: [],
            loading: false,
            id:''
        }
    },
    computed: {},
    created() {

    },
    methods: {
        cancelClick() {
            this.isShow = false
        },
        showLog(id){
            this.id = id
            this.isShow = true
            this.searchData()
        },
        searchData() {
            this.loading = true
            this.dataList = []
            let param = {
                url: `operatorlog/query`,
                method: 'POST',
                data: {
                    id: this.id,
                    logType: this.logType
                }
            }

            this.$http(param).then((res) => {
                if (res && !isEmpty(res.data)) {
                    this.dataList = res.data
                } else {
                    this.dataList = []
                }
                this.loading = false
            }).catch(err => {
                console.log(err);
                this.dataList = []
                this.loading = false
            })

        },

    }
}
</script>
