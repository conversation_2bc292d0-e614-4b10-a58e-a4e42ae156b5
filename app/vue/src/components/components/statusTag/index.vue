<template>
    <div>
        <el-tag v-if="status===1" type="success">有效</el-tag>
        <el-tag v-if="status===-1" type="danger">删除</el-tag>
        <el-tag v-if="status===0" type="danger">无效</el-tag>
    </div>
</template>

<script>
module.exports = {
    props: {
        status: {
            type: Number,
            required: true,
        },
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style>


</style>
