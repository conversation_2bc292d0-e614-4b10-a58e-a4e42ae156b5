<template>
    <div>
        <el-select class="w100" ref="sel" reserve-keyword v-model="sonValue" :multiple="isMultiple" filterable clearable
                   :placeholder="text" :disabled="isDisabled"
                   :loading="loading" @change="changeSelect" @focusout.native="input"
        >
            <el-row v-if="isMultiple">
                <el-col :span="12">
                    <el-tag type="primary" class="w100" style="text-align: center;cursor: pointer"
                            @click="selectAllClick">
                        全选列表值
                    </el-tag>
                </el-col>
                <el-col :span="12">
                    <el-tag type="warning" class="w100" style="text-align: center;cursor: pointer"
                            @click="clearAllClick">
                        清空列表值
                    </el-tag>
                </el-col>
            </el-row>

            <el-option :label="formatLabel(item)"
                       :value="formatValue(item)"
                       :key="item.id"
                       v-for="item in options">
            </el-option>
        </el-select>
    </div>
</template>
<script>


import {isEmpty} from "@/utils/util";

export default {
    props: {
        value: {
            type: [String, Number, Array],
            default: ''
        },
        text: {
            type: String,
            default: '请选择'
        },
        isDisabled: {
            type: Boolean,
            default: false
        },
        storeData: {
            type: Object,
            default: null
        },
        isMultiple: {
            type: Boolean,
            default: false
        },
    },
    model: {
        prop: 'value',
        event: 'valueChange'
    },
    data() {
        return {
            options: [],
            sonValue: this.value,
            loading: false,
            tagLabel:''
        }
    },
    watch: {
        value(newVal, oldVal) {
            this.sonValue = newVal
        }
    },
    created() {
        this.searchData()

    },
    methods: {
        input(){
            if(this.$refs.sel && this.$refs.sel.$refs.input){
                this.tagLabel = this.$refs.sel.$refs.input.value
            }else {
                this.tagLabel = ''
            }
        },
        selectAllClick() {
            let optValList = this.options.filter(s => this.formatLabel(s).toLowerCase().includes(this.tagLabel.toLowerCase())).map(s => s.value).filter(s => !this.sonValue.includes(s))
            this.sonValue.push(...optValList)
            this.$emit('valueChange', this.sonValue)
            this.changeSelect(this.sonValue)
        },
        clearAllClick() {
            let optValList = this.options.filter(s => this.formatLabel(s).toLowerCase().includes(this.tagLabel.toLowerCase())).map(s => s.value)
            this.sonValue = this.sonValue.filter(s => !optValList.includes(s))
            this.$emit('valueChange', this.sonValue)
            this.changeSelect(this.sonValue)
        },
        formatLabel(item) {
            return `${item.desc}-${item.value}`
        },
        formatValue(item) {
            return item.value
        },
        searchData() {

            this.loading = true
            let param = {
                url: `commondata/orderChannelList`,
                method: 'GET',
            }

            this.$http(param).then((res) => {
                console.log(res)
                if (res && !isEmpty(res.data)) {
                    this.options = res.data
                } else {
                    this.options = []
                }
                this.loading = false
            }).catch(err => {
                console.log(err);
                this.options = []
                this.loading = false
            })

        },
        changeSelect(value) {
            if (Array.isArray(value)) {
                let list = this.options.filter(s => value.indexOf(s.value) > -1)
                let tarList = []
                if (!isEmpty(list)) {
                    tarList = list.map(s => {
                        return {
                            desc: s.desc || '',
                            value: s.value || '',
                        }
                    })
                }
                this.$emit('callbackchange', tarList)
            } else {
                let obj = this.options.find(son => son.value === value) || {}

                this.$emit('callbackchange', {
                    desc: obj.desc || '',
                    value: obj.value || '',
                })
            }
            this.$emit('valueChange', this.sonValue)

        }
    }
}

</script>

