<template>
    <div v-loading="loading">
        <el-table class="f12" ref="multipleTable"  :data="tableList" @row-dblclick="dblclick"
                  @selection-change="btnSelectionChange" size="small" :highlight-current-row="true" border stripe style="width: 100%;">
            <slot></slot>
        </el-table>
        <div class="pagination_box df">
            <div>
                <p class="f14">
                    共{{ total }}条
                    <el-select class="table_page_size" style="width: 110px"
                               v-model="pageSize"
                               placeholder="请选择页码"
                               size="mini"
                               @change="pageSizeChange">
                        <el-option label="10条每页" :value="10"></el-option>
                        <el-option label="20条每页" :value="20"></el-option>
                        <el-option label="50条每页" :value="50"></el-option>
                    </el-select>
                </p>
            </div>
            <el-pagination
                    background
                    layout="prev, pager, next,jumper"
                    :total="total"
                    :current-page="currentPage"
                    :page-size="pageSize"
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
            >
            </el-pagination>

        </div>
    </div>
</template>

<script>
import {isEmpty} from '@/utils/util'

export default {
    props: {
        requestParam: {
            type: Object,
            default() {
                return {}
            }
        }

    },
    data() {
        return {
            loading: false,
            tableList: [],
            total: 0,
            currentPage: 1,
            pageSize: 10,
            selection: []
        }
    },
    methods: {
        pageSizeChange() {
            this.searchData()
        },
        dblclick(item) {
            this.$emit('onRowDblclick', item)
        },
        handleCurrentChange(value) {
            // 如果 在也面把pagesize 从10 改成 50 ,这时候正好页面在最后一页的话,会同时出发bakcPage 和 sizeChangeBack ,而这是只需要一个方法执行,这边就加上loading  限制就行了
            if (!this.loading) {
                console.log('handleCurrentChange:' + value)
                this.currentPage = value
                this.searchData()
            }
        },
        btnSelectionChange(list) {
            this.selection = list
            this.$emit('selectionChange', list)
        },
        handleSizeChange(val) {
            if (!this.loading) {
                let curPage = this.currentPage
                console.log('handleSizeChange:' + curPage)
                this.pageSize = val
                this.searchData()
            }
        },
        searchData(pageNum) {
            this.loading = true
            if (pageNum) {
                this.currentPage = pageNum
            }

            let config = {
                ...this.requestParam,
            }

            if (config.data) {
                config.data.page = this.currentPage
                config.data.pageSize = this.pageSize
            }

            this.$http(config)
                .then(result => {
                    if (result.success) {
                        if (!this.needContinue(result)) {
                            return
                        }

                        if (result && result.data && !isEmpty(result.data.data)) {
                            result.data.data.forEach(s => {
                                s.loading = false
                            })

                            this.tableList = result.data.data
                            this.total = result.data.paginator.totalCount
                        } else {
                            this.tableList = []
                            this.total = 0
                        }
                    } else {
                        this.$message.error(result.msg || '查询失败')
                    }
                    this.loading = false
                })
                .catch(e => {
                    console.warn(e)
                    this.tableList = []
                    this.total = 0
                    this.loading = false
                })
        },
        needContinue(result) {
            let maxPageNum = Math.ceil(result.data.paginator.totalCount / parseFloat(this.pageSize))
            if (maxPageNum === 0) {
                this.tableList = []
                this.total = 0
                this.loading = false
                return false
            }

            if (this.currentPage > maxPageNum) {
                this.currentPage = maxPageNum
                console.log('needContinue:' + maxPageNum)
                this.searchData()
                return false
            }
            return true
        }
    }
}
</script>
