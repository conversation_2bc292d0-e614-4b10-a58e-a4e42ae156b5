<template>
    <div>
        <div class="topmtitle">
            <p class="text">
            <slot name="text">{{text}}</slot>
            </p>
        </div>
        <slot></slot>

    </div>
</template>

<script>
export default {
    props: {
        text: {
            type: String,
            default: ''
        }
    }
}
</script>

<style lang="scss" scoped>
    .topmtitle {
        padding-left: 10px;
        margin-bottom: 20px;
        border-left: 4px solid #3a8ee6;

        .text {
            padding: 3px 0;
            border-bottom: 1px solid #3a8ee6;
            line-height: 1;
            font-size: 14px;
            color: #333;
        }
    }

    .bottommtitle {
        padding-left: 10px;
        margin-bottom: 20px;
        border-left: 0px solid #3a8ee6;

        .text {
            padding: 3px 0;
            border-bottom: 1px solid #3a8ee6;
            line-height: 1;
            font-size: 14px;
            color: #333;
        }
    }
</style>
