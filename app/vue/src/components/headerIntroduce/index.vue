<template>
  <el-row class="introduce-row">
    <div class="introduce-header" @click="change" :style="cusStyle">
      <span>{{ title }}{{ suffix }} </span>
      <div class="introduce-header-filter standard-input">
        <slot name="filter"></slot>
      </div>
      <span class="introduce-header_expand" v-show="disabledArrow == 'false'">
        <i class="el-icon-arrow-down" v-if="show"></i>
        <i class="el-icon-arrow-right" v-if="!show"></i>
      </span>
    </div>
    <div class="introduce-body">
      <el-collapse-transition>
        <div v-show="show">
          <!-- loading -->
          <template v-if="!loading">
            <!-- 向子槽下发显示隐藏事件 -->
            <slot :show="show"></slot>
          </template>
          <template v-else>
            <!-- 用 skeleton 代替空白页面-->
            <el-skeleton animated style="padding: 20px"/>
          </template>
        </div>
      </el-collapse-transition>
    </div>
  </el-row>
</template>

<script>
module.exports = {
  props: {
    title: {
      type: String,
      required: true,
    },
    loading: {
      type: Boolean,
      default: function () {
        return false;
      }
    },
    disabledArrow: {
      type: String,
      required: false,
      default: function () {
        return "false";
      }
    },
    fold: {
      type: Boolean,
      required: false,
      default: function () {
        return true;
      }
    }
  },
  data: function () {
    return {
      show: true,
      cusStyle: {
        backgroundColor: '',
      },
      suffix: ''
    }
  },
  mounted: function () {
    this.show = this.fold == true;
  },
  methods: {
    change() {
      if (this.disabledArrow == "true") {
        return;
      }
      this.show = !this.show;
    },
    changeColor(color) {
      this.cusStyle.backgroundColor = color;
    },
    changeSuffix(suffix) {
      this.suffix = suffix;
    }
  }
}
</script>

<style>


</style>
