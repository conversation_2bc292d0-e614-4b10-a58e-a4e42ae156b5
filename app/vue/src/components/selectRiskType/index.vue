<template>
    <div>
        <el-select class="w100" v-model="sonValue" filterable clearable :placeholder="text" :disabled="isDisabled"
                   :loading="loading" @change="changeSelect">
            <el-option :label="formatLabel(item)"
                       :value="formatValue(item)"
                       :key="item.id"
                       v-for="item in options">
            </el-option>
        </el-select>
    </div>
</template>
<script>


import {isEmpty} from "@/utils/util";

export default {
    props: {
        value: {
            type: [String, Number],
            default: ''
        },
        text: {
            type: String,
            default: '请选择'
        },
        isDisabled: {
            type: Boolean,
            default: false
        },
        storeData: {
            type: Object,
            default: null
        },
    },
    model: {
        prop: 'value',
        event: 'valueChange'
    },
    data() {
        return {
            options: [],
            sonValue: this.value,
            loading: false
        }
    },
    watch: {
        value(newVal, oldVal) {
            this.sonValue = newVal
        }
    },
    created() {
        this.searchData()

    },
    methods: {
        formatLabel(item) {
            return `${item.desc}`
        },
        formatValue(item) {
            return item.value
        },
        searchData() {

            this.loading = true
            let param = {
                url: `commondata/riskTypeList`,
                method: 'GET',
            }

            this.$http(param).then((res) => {
                if (res && !isEmpty(res.data)) {
                    this.options = res.data
                } else {
                    this.options = []
                }
                this.loading = false
            }).catch(err => {
                console.log(err);
                this.options = []
                this.loading = false
            })

        },
        changeSelect(value) {
            let obj = this.options.find(son => son.value === value) || {}

            this.$emit('callbackchange', {
                desc: obj.desc || '',
                value: obj.value || '',
            }, this.storeData)
            this.$emit('valueChange', this.sonValue)
        }
    }
}

</script>

