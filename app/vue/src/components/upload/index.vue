<template>
    <div>
        <el-upload class="upload-demo" name="file" action="../inner/upload/upload"
                   :on-success="onSuccess"
                   :on-remove="onRemove"
                   :on-preview="handlePreview"
                   :on-exceed="onExceed"
                   :file-list="fileList"
                   :limit="limit"
        >
            <el-row>
                <el-button size="mini" icon="el-icon-upload2" type="primary">点击上传</el-button>
            </el-row>
            <el-row v-if="remark!==''">
                <span style="color: red"> {{ remark }}</span>
            </el-row>
        </el-upload>

        <ElImageViewer
                v-if="showViewer"
                :initial-index="0"
                :zIndex="3000"
                :on-close="()=>{showViewer=false}"
                :url-list="previewImgUrlList">
        </ElImageViewer>
    </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import {uuid} from '@/utils'

export default {
    props: {
        storeData: {
            type: Object,
            default() {
                return {}
            }
        },
        value: {
            type: [String, Array],
            default: ''
        },
        remark: {
            type: String,
            default: ''
        },
        //image, file
        bindType: {
            type: String,
            default: 'image'
        },
        limit: {
            type: Number,
            default: 1
        }
    },
    model: {
        prop: 'value',
        event: 'valueChange'
    },
    components: {
        ElImageViewer
    },
    data() {
        return {
            fileList: [],
            showViewer: false,
            previewImgUrlList: [],
            sonValue: this.value
        }
    },
    computed: {},
    watch: {
        value(newVal, oldVal) {
            if (newVal) {
                this.fillFileList()
            }
            this.$parent.$emit('el.form.change')
        }
    },
    created() {
        if (this.value) {
            this.fillFileList()
        }
    },
    methods: {
        fillFileList() {
            if (this.limit > 1) {
                this.fileList = this.value.map(s => {
                    return {name: this.getFileName(s), url: s}
                })
            } else {
                let fileName = this.getFileName(this.value)
                this.fileList = [{name: fileName, url: this.value}]
            }
        },
        downloadFile(e) { // 点击上传的文件列表的文件的操作
            var a = document.createElement("a");

            console.log(a, "创建a");

            var event = new MouseEvent("click");

            a.target = "_blank";
            a.download = e.name;
            a.href = e.url;//路径前拼上前缀，完整路径
            a.dispatchEvent(event);
        },
        getFileName(path) {
            const fileFormat = path.slice(path.lastIndexOf('.') + 1);
            return uuid() + '.' + fileFormat;
        },
        handlePreview(file) {
            if (this.bindType === 'file') {
                this.downloadFile(file)
                return
            }

            this.showViewer = true
            this.previewImgUrlList = [file.url]
        },
        onSuccess(result, file, fileList) {
            let imgUrl = ''
            if (result && result.data) {
                imgUrl = result.data.url
            }
            if (imgUrl) {
                this.fileList.push({name: this.getFileName(imgUrl), url: imgUrl})
            }

            if (this.limit > 1) {
                this.sonValue = this.fileList.map(s => s.url)
            } else {
                this.sonValue = imgUrl
            }
            this.$emit('valueChange', this.sonValue)

            this.$emit('callbackSuccess', {
                imgUrl: imgUrl,
                storeData: this.storeData
            })
        },
        onRemove(file, fileList) {
            if (this.limit > 1) {
                let index = this.fileList.findIndex(s => s.name === file.name)
                if (index > -1) {
                    this.fileList.splice(index, 1)
                }
                this.sonValue = this.fileList.map(s => s.url)
            } else {
                this.sonValue = ''
            }

            this.$emit('valueChange', this.sonValue)
            this.$emit('callbackRemove', {
                storeData: this.storeData
            })
        },
        onExceed() {
            this.$message.error(`只能上传${this.limit}张图片`)
        }
    }
}
</script>

<style lang="scss" scoped>
</style>
