<template>
    <div id="app">
        <AppMain/>
    </div>

</template>

<script>
import AppMain from "./components/AppMain";
import { setSession } from '@/utils/util'


export default {
    name: "Layout",
    components: {
        AppMain,
    },
    data() {
        return {
            collapse: false,
            /**
             * tips菜单页面 当前使用/config/config-tab.json
             * 后续接入权限后可以从同一权限接口中载入
             */
            menuData: [],
            /**
             * 允许多开的页面
             * menuData>>item>>code
             */
            multTabs: ["home-2"]
        }
    },
    mounted() {
    },
    methods: {
      queryPermissions(){
          let param = {
              url: `userinfo/queryPermissionList`,
              method: 'POST'
          }

          this.$http(param).then((res) => {
              if (res && res.success) {
                  setSession('permissionList', res.data)
              }
          }).catch(err => {
              console.log(err)
          })
      }
    },
    created() {
        this.queryPermissions()
    }
};
</script>

<style lang="scss" scoped>

</style>
