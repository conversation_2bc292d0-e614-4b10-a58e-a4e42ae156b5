body {
  font-family: "Source Han Sans SC", "Noto Sans CJK SC", "HanHei SC",
  "方正兰亭黑_GB18030", "方正兰亭黑_GBK", system-ui, -apple-system, sans-serif,
  "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji",
  emoji;
  background: white;
}

td > .el-link {
  padding-right: 5px;
}

.el-dialog__header {
  border-bottom: 1px solid #ccc;
}

.el-dialog__body {
  padding: 20px 30px !important;
}

.el-dialog__body .el-form-item__label::after {
  content: ":";
  font-size: 16px;
  padding: 0 5px;
}

.no-border-radius input, .no-border-radius .el-date-editor, .no-border-radius textarea {
  border-radius: 0 !important;
}

.standard-input input, .standard-input .el-date-editor {
  border-radius: 0 !important;
}

.standard-input, .standard-input .el-checkbox__label, .standard-input .el-form-item__content, .standard-input .el-form-item__label, td .el-link, td .el-button {
  font-size: 12px !important;
  font-weight: normal !important;
}

.standard-input .el-form-item {
  width: 100%;
  margin-bottom: 10px !important;

  display: flex !important;
  flex-direction: row;
}

.standard-input .el-form-item {
  margin-right: 0 !important;
}

.standard-input .el-form-item__label {
  position: absolute;
}

.standard-input .el-form-item__content {
  width: 100%;
  padding-left: 80px;
}

.standard-input .el-select, .el-input_inner, .el-date-editor--datetimerange.el-input__inner, .el-date-editor--daterange.el-input__inner {
  width: 100%;
}

.standard-input .el-checkbox {
  margin-right: 10px;
}

.el-table, .el-table thead, .el-descriptions-row > th, .el-descriptions-row > td {
  font-size: 12px !important;
  color: #333333 !important;
}

.el-descriptions-item__label {
  width: 150px;
}

.el-descriptions-item__content {
  word-break: break-word;
}

@media screen and (min-width: 1792px) {
  .el-descriptions-item__label {
    width: 150px;
  }
}

@media screen and (min-width: 1660px) and (max-width: 1792px) {
  .el-descriptions-item__label {
    width: 130px;
  }
}

@media screen and (min-width: 960px) and (max-width: 1660px) {
  .el-descriptions-item__label {
    width: 100px;
  }

  .vue-clipboard {
    margin-left: 2px !important;
  }
}

@media screen and (max-width: 960px) {
  .el-descriptions-item__label {
    width: 90px;
  }

  .vue-clipboard {
    margin-left: 0px !important;
  }
}

.order-descriptions-label {
  background-color: #f5f5f5 !important;
  font-weight: 400 !important;
  text-align: center !important;
}

.el-table__header th {
  background-color: #f5f5f5 !important;
  font-weight: 400 !important;
}


.el-descriptions .is-bordered {
  table-layout: fixed !important;
}

.el-dialog__body .el-form-item {
  margin-bottom: 10px;
}

.is-error.el-form-item {
  margin-bottom: 22px !important;
}

.content-center {
  text-align: center;
}

.detail_fixed_header {
  z-index: 99;
  background-color: #fff;
}

.el-button--primary {
  background-color: #419EFF !important;
  border-color: #419EFF !important;
  color: #fff !important;
}

.el-button--primary:hover {
  background-color: #8DC5FF !important;
  border-color: #8DC5FF !important;
  color: #fff !important;
}

.el-table__body tr.current-row > td.el-table__cell {
  background-color: #F5F7FA !important;
}

.introduce-row {
  margin-bottom: 10px;
}

.introduce-header {
  background-color: #419EFF;
  color: #fff;
  padding: 5px 10px;

  cursor: pointer;
}


.introduce-header i {
  margin: 3px 0 0 0;
  background-color: #fff;
  color: #419EFF;
  border-radius: 50%;

  float: right;
}

.introduce-row {
  margin-bottom: 10px;
  position: relative;
}

.introduce-header_expand {
  margin: 3px 0 0 0;
  float: right;
}

.introduce-header_expand i {
  background-color: #fff;
  color: #419EFF;
  border-radius: 50%;
}

.introduce-body {
  border: 1px solid #419EFF;
}

.introduce-header-filter {
  display: inline-block;
  margin-left: 10px;
  position: absolute;
  top: 5px;
}


#app .el-pagination.is-background .el-pager li:not(.disabled).active {
  color: #fff !important;
  background-color: #68B1FF;
}

#app .el-pagination.is-background .el-pager li:not(.disabled):hover {
  color: #419EFF;
}


.el-table .warning-row {
  color: #ff6f00;
}

.text-no-select {
  user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
}


.custom-content-wrapper {
  padding: 10px;
}

.pagination_box {
  width: 100%;
  height: 100px;
  align-items: center;
  justify-content: end;
  padding-right: 30px;

  .table_page_size {
    width: 76px;
  }
}

.introduce-row {
  margin-bottom: 10px;
}

.introduce-header {
  background-color: #419EFF;
  color: #fff;
  padding: 5px 10px;
  font-size: 14px;
  cursor: pointer;
}


.introduce-header i {
  margin: 3px 0 0 0;
  background-color: #fff;
  color: #419EFF;
  border-radius: 50%;

  float: right;
}

.introduce-row {
  margin-bottom: 10px;
  position: relative;
}

.introduce-header_expand {
  margin: 3px 0 0 0;
  float: right;
}

.introduce-header_expand i {
  background-color: #fff;
  color: #419EFF;
  border-radius: 50%;
}

.introduce-body {
  border: 1px solid #419EFF;
}

.introduce-header-filter {
  display: inline-block;
  margin-left: 10px;
  position: absolute;
  top: 5px;
}
