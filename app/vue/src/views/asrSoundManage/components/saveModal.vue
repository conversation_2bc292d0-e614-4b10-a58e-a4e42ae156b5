<template>
    <el-dialog class="edit_pop_div" :visible="isShow" :title="'审核'"
               :close-on-click-modal="false" @close="cancelClick"
               :append-to-body="true" width="50%" center>
        <el-form label-width="140px"
                 :model="formData"
                 size="small"
                 ref="formData"
                 >
            <el-form-item label="文件">
                <el-card class="box-card">
                    <div v-for="(textObj,index) in speechToTextObj" class="text item" :key="index">
                        <template>
                            <span v-if="isAudio(textObj.resourceUrl)">
                                {{speechToTextObj.indexOf(textObj) + 1 + '号音频文件：  ' }}
                                <br>
                                下载：<a :href="textObj.resourceUrl" >  {{textObj.fileName}}</a>
                                <br>
                                预览：<audio controls ref="audio">
                                    <source :src="textObj.resourceUrl" />
                                </audio>
                                <br>
                                {{'音频文件内容：' + textObj.text}}
                            </span>
                            <span v-if="isVideo(textObj.resourceUrl)">
                                {{speechToTextObj.indexOf(textObj) + 1 + '号视频文件：' }}
                                <br>
                                下载：<a :href="textObj.resourceUrl" >{{textObj.fileName}}</a>
                                <br>
                                预览：
                                <div class="video-container">
                                    <video
                                        :id="'video-player-' + index"
                                        :ref="'videoPlayer' + index"
                                        controls
                                        preload="auto"
                                        width="320"
                                        height="240"
                                        :poster="defaultPoster"
                                        @loadstart="onVideoLoadStart(index)"
                                        @error="onVideoError(index, textObj)"
                                        @canplay="onVideoCanPlay(index)">
                                        <source :src="textObj.resourceUrl" :type="getVideoMimeType(textObj.resourceUrl)"/>
                                        <p>您的浏览器不支持视频播放</p>
                                    </video>
                                    <div v-if="videoErrors[index]" class="video-error">
                                        <p style="color: #f56c6c; margin: 5px 0;">
                                            视频加载失败，正在尝试其他播放方式...
                                        </p>
                                        <div :id="'fallback-player-' + index" class="fallback-player"></div>
                                    </div>
                                </div>
                                <br>
                                {{'视频文件内容：' + textObj.text}}
                            </span>
                        </template>
                        <el-divider v-if="speechToTextObj.indexOf(textObj) < speechToTextObj.length - 1"/>
                    </div>
                </el-card>
            </el-form-item>
            <el-form-item label="检查员司机服务">
                <span style="color: #3a8ee6">{{formData.serviceTag}}</span>
            </el-form-item>
            <el-form-item label="检查员点评">
                <span style="color: #3a8ee6">{{formData.serviceReview}}</span>
            </el-form-item>
            <el-form-item label="检查员评分">
                <el-input-number :min="0" :max="5" :precision="2" :step="0.1" v-model="formData.ratingScore" disabled></el-input-number>
            </el-form-item>
            <el-form-item label="审核意见" prop="auditComment">
                <el-input type="textarea" v-model="formData.auditComment" maxlength="256" :autosize="{minRows: 5, maxRows: 10}"></el-input>
            </el-form-item>

            <el-form-item label="上传是否合格" prop="uploadStatus">
              <el-radio v-model="formData.uploadStatus" :label="2">不合格</el-radio>
              <el-radio v-model="formData.uploadStatus" :label="3">合格</el-radio>
            </el-form-item>

          <el-form-item label="司机是否违规" prop="driverViolated">
            <el-radio v-model="formData.driverViolated" :label="1">违规</el-radio>
            <el-radio v-model="formData.driverViolated" :label="0">不违规</el-radio>
          </el-form-item>

          <el-form-item label="司机服务评分" prop="driverScore">
            <el-input-number :min="0" :max="5" :precision="2" :step="0.1" v-model="formData.driverScore"></el-input-number>
          </el-form-item>

        </el-form>

        <span slot="footer" class="dialog-footer">
       <el-button type="default" size="mini" @click="cancelClick">
            取消
        </el-button>
        <el-button type="primary" :loading="loadingSave" size="mini" @click="submitClick">
            保存
        </el-button>
    </span>
    </el-dialog>

</template>

<script>
export default {
    props: {
        editInfo: {
            type: Object,
            default() {
                return {}
            }
        },
        isShow: {
            type: Boolean,
            default: false
        },
    },

    data: function () {
        return {
            formData: {},
            speechToTextObj: [{
                resourceUrl:"",
                text:"",
                fileName:""
            }],
            loadingSave: false,
            videoErrors: {},
            defaultPoster: "//pic5.40017.cn/i/ori/PS2lfS0492.jpg",
            videoJsPlayers: {},
        }
    },
    created() {
        this.formData = this.deepCopy(this.editInfo)
    },
    mounted() {
      this.querySoundToText()
      this.loadVideoJsIfNeeded()
    },
    beforeDestroy() {
        // 清理 Video.js 实例
        Object.keys(this.videoJsPlayers).forEach(key => {
            if (this.videoJsPlayers[key] && typeof this.videoJsPlayers[key].dispose === 'function') {
                this.videoJsPlayers[key].dispose()
            }
        })
    },
    methods: {
        isAudio(filePath){
            let path = filePath.split("?")[0];
            return path.includes('.mp3')
                || path.includes('.wav')
                || path.includes('.flac')
                || path.includes('.aac')
                || path.includes('.m4a')
                || path.includes('.wma')
                || path.includes('.ogg')
        },
        isVideo(filePath){
            let path = filePath.split("?")[0];
            return path.includes('.mp4')
                || path.includes('.avi')
                || path.includes('.mov')
                || path.includes('.wmv')
                || path.includes('.flv')
                || path.includes('.mkv')
                || path.includes('.webm')
        },
        getVideoMimeType(filePath) {
            let path = filePath.split("?")[0].toLowerCase();
            if (path.includes('.mp4')) return 'video/mp4';
            if (path.includes('.avi')) return 'video/x-msvideo';
            if (path.includes('.mov')) return 'video/quicktime';
            if (path.includes('.wmv')) return 'video/x-ms-wmv';
            if (path.includes('.flv')) return 'video/x-flv';
            if (path.includes('.mkv')) return 'video/x-matroska';
            if (path.includes('.webm')) return 'video/webm';
            if (path.includes('.ogg')) return 'video/ogg';
            return 'video/mp4'; // 默认
        },
        loadVideoJsIfNeeded() {
            // 动态加载 Video.js
            if (typeof window.videojs === 'undefined') {
                const script = document.createElement('script');
                script.src = 'https://vjs.zencdn.net/8.6.1/video.min.js';
                script.onload = () => {
                    console.log('Video.js loaded successfully');
                };
                document.head.appendChild(script);

                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://vjs.zencdn.net/8.6.1/video-js.css';
                document.head.appendChild(link);
            }
        },
        onVideoLoadStart(index) {
            console.log(`Video ${index} loading started`);
        },
        onVideoCanPlay(index) {
            console.log(`Video ${index} can play`);
            this.$set(this.videoErrors, index, false);
        },
        onVideoError(index, textObj) {
            console.log(`Video ${index} error, trying fallback`);
            this.$set(this.videoErrors, index, true);

            // 延迟执行以确保DOM已更新
            this.$nextTick(() => {
                this.initFallbackPlayer(index, textObj);
            });
        },
        initFallbackPlayer(index, textObj) {
            const fallbackId = `fallback-player-${index}`;
            const fallbackElement = document.getElementById(fallbackId);

            if (!fallbackElement) {
                console.error(`Fallback element ${fallbackId} not found`);
                return;
            }

            // 清空容器
            fallbackElement.innerHTML = '';

            // 检查是否已加载 Video.js
            if (typeof window.videojs !== 'undefined') {
                this.createVideoJsPlayer(fallbackElement, index, textObj);
            } else {
                // 如果 Video.js 还未加载，等待加载完成
                const checkVideoJs = setInterval(() => {
                    if (typeof window.videojs !== 'undefined') {
                        clearInterval(checkVideoJs);
                        this.createVideoJsPlayer(fallbackElement, index, textObj);
                    }
                }, 100);

                // 10秒后停止检查
                setTimeout(() => {
                    clearInterval(checkVideoJs);
                    this.createBasicFallback(fallbackElement, textObj);
                }, 10000);
            }
        },
        createVideoJsPlayer(container, index, textObj) {
            try {
                // 创建 video 元素
                const videoElement = document.createElement('video');
                videoElement.className = 'video-js vjs-default-skin';
                videoElement.controls = true;
                videoElement.preload = 'auto';
                videoElement.width = 320;
                videoElement.height = 240;
                videoElement.setAttribute('data-setup', '{}');

                container.appendChild(videoElement);

                // 初始化 Video.js 播放器
                const player = window.videojs(videoElement, {
                    controls: true,
                    preload: 'auto',
                    width: 320,
                    height: 240,
                    fluid: false,
                    responsive: false,
                    sources: [{
                        src: textObj.resourceUrl,
                        type: this.getVideoMimeType(textObj.resourceUrl)
                    }],
                    html5: {
                        vhs: {
                            overrideNative: true
                        },
                        nativeVideoTracks: false,
                        nativeAudioTracks: false,
                        nativeTextTracks: false
                    }
                });

                // 保存播放器实例以便后续清理
                this.videoJsPlayers[`fallback-${index}`] = player;

                player.ready(() => {
                    console.log(`Video.js player ${index} is ready`);
                });

                player.on('error', () => {
                    console.log(`Video.js player ${index} also failed, showing basic fallback`);
                    this.createBasicFallback(container, textObj);
                });

            } catch (error) {
                console.error('Error creating Video.js player:', error);
                this.createBasicFallback(container, textObj);
            }
        },
        createBasicFallback(container, textObj) {
            container.innerHTML = `
                <div style="border: 1px dashed #ccc; padding: 20px; text-align: center; width: 320px; height: 240px; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <p style="margin: 0 0 10px 0; color: #666;">无法播放此视频格式</p>
                    <p style="margin: 0 0 10px 0; font-size: 12px; color: #999;">请下载文件使用本地播放器观看</p>
                    <a href="${textObj.resourceUrl}" download style="color: #409EFF; text-decoration: none;">
                        📥 下载视频文件
                    </a>
                </div>
            `;
        },
        querySoundToText(){
          let param = {
            url: 'sound/querySoundToText',
            method: 'POST',
            data: this.formData
          }
          this.$http(param).then((res) => {
            if (res.success) {
              this.speechToTextObj = res.data.data
          // this.speechToTextObj[0].resourceUrl = "http://file.40017.cn/workweixinjsapi/voice/1.mov"
            } else {
              this.$message.error(res.msg)
            }
          }).catch(err => {
            console.log(err);
            this.$message.error('查询语音转文字失败')
          })
        },
        submitClick() {
            this.$refs.formData.validate((valid) => {
                if (this.formData.uploadStatus !== 2 && this.formData.uploadStatus !== 3){
                  valid = false
                  this.$message.error('请选择上传是否合格')
                }
                if (valid) {
                    this.loadingSave = true
                    let param = {
                        url: 'sound/audit',
                        method: 'POST',
                        data: this.formData
                    }
                    this.$http(param).then((res) => {
                        if (res.success) {
                            this.$message.success('保存成功')
                            this.$emit('callbackclose', 'success')
                        } else {
                            this.$message.error(res.msg)
                        }
                        this.loadingSave = false

                    }).catch(err => {
                        console.log(err);
                        this.$message.error('保存失败')
                        this.loadingSave = false
                    })
                }
            });

        },
        //取消
        cancelClick() {
            this.$emit('callbackclose')
        },
        // 深拷贝方法（如果不存在的话）
        deepCopy(obj) {
            if (obj === null || typeof obj !== 'object') return obj;
            if (obj instanceof Date) return new Date(obj.getTime());
            if (obj instanceof Array) return obj.map(item => this.deepCopy(item));
            if (typeof obj === 'object') {
                const copy = {};
                Object.keys(obj).forEach(key => {
                    copy[key] = this.deepCopy(obj[key]);
                });
                return copy;
            }
        }
    }
}
</script>

<style scoped>
.video-container {
    margin: 10px 0;
}

.video-error {
    margin-top: 10px;
}

.fallback-player {
    margin-top: 10px;
}

/* Video.js 样式覆盖 */
.video-js {
    font-family: Arial, Helvetica, sans-serif;
}

.video-js .vjs-big-play-button {
    font-size: 2.5em;
    line-height: 2.3;
    height: 2.5em;
    width: 2.5em;
    border-radius: 50%;
    background-color: rgba(43, 51, 63, 0.7);
    border: 0.15em solid #fff;
    color: #fff;
}

.video-js .vjs-big-play-button:hover {
    background-color: rgba(43, 51, 63, 0.9);
}
</style>
