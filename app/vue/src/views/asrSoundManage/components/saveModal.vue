<template>
    <el-dialog class="edit_pop_div" :visible="isShow" :title="'审核'"
               :close-on-click-modal="false" @close="cancelClick"
               :append-to-body="true" width="50%" center>
        <el-form label-width="140px"
                 :model="formData"
                 size="small"
                 ref="formData"
                 >
            <el-form-item label="文件">
                <el-card class="box-card">
                    <div v-for="(textObj,index) in speechToTextObj" class="text item">
                        <template>
                            <span v-if="isAudio(textObj.resourceUrl)">
                                {{speechToTextObj.indexOf(textObj) + 1 + '号音频文件：  ' }}
                                <br>
                                下载：<a :href="textObj.resourceUrl" >  {{textObj.fileName}}</a>
                                <br>
                                预览：<audio controls ref="audio">
                                    <source :src="textObj.resourceUrl" />
                                </audio>
                                <br>
                                {{'音频文件内容：' + textObj.text}}
                            </span>
                            <span v-if="isVideo(textObj.resourceUrl)">
                                {{speechToTextObj.indexOf(textObj) + 1 + '号视频文件：' }}
                                <br>
                                下载：<a :href="textObj.resourceUrl" >{{textObj.fileName}}</a>
                                <br>
                                预览：<video id="my-player"
                                        controls
                                        class="video-js"
                                        preload="auto"
                                        poster="//pic5.40017.cn/i/ori/PS2lfS0492.jpg"
                                        width="320" height="240"
                                        data-setup='{}'>
                                    <source :src="textObj.resourceUrl" type="video/mp4"/>
                                    <source :src="textObj.resourceUrl" type="video/ogg"/>
                                    <source :src="textObj.resourceUrl" type="video/x-msvideo"/>
                                    <source :src="textObj.resourceUrl" type="video/webm"/>
                                </video>
                                <br>
                                {{'视频文件内容：' + textObj.text}}
                            </span>
                        </template>
                        <el-divider v-if="speechToTextObj.indexOf(textObj) < speechToTextObj.length - 1"/>
                    </div>
                </el-card>
            </el-form-item>
            <el-form-item label="检查员司机服务">
                <span style="color: #3a8ee6">{{formData.serviceTag}}</span>
            </el-form-item>
            <el-form-item label="检查员点评">
                <span style="color: #3a8ee6">{{formData.serviceReview}}</span>
            </el-form-item>
            <el-form-item label="检查员评分">
                <el-input-number :min="0" :max="5" :precision="2" :step="0.1" v-model="formData.ratingScore" disabled></el-input-number>
            </el-form-item>
            <el-form-item label="审核意见" prop="auditComment">
                <el-input type="textarea" v-model="formData.auditComment" maxlength="256" :autosize="{minRows: 5, maxRows: 10}"></el-input>
            </el-form-item>

            <el-form-item label="上传是否合格" prop="uploadStatus">
              <el-radio v-model="formData.uploadStatus" :label="2">不合格</el-radio>
              <el-radio v-model="formData.uploadStatus" :label="3">合格</el-radio>
            </el-form-item>

          <el-form-item label="司机是否违规" prop="driverViolated">
            <el-radio v-model="formData.driverViolated" :label="1">违规</el-radio>
            <el-radio v-model="formData.driverViolated" :label="0">不违规</el-radio>
          </el-form-item>

          <el-form-item label="司机服务评分" prop="driverScore">
            <el-input-number :min="0" :max="5" :precision="2" :step="0.1" v-model="formData.driverScore"></el-input-number>
          </el-form-item>

        </el-form>

        <span slot="footer" class="dialog-footer">
       <el-button type="default" size="mini" @click="cancelClick">
            取消
        </el-button>
        <el-button type="primary" :loading="loadingSave" size="mini" @click="submitClick">
            保存
        </el-button>
    </span>
    </el-dialog>

</template>

<script>
export default {
    props: {
        editInfo: {
            type: Object,
            default() {
                return {}
            }
        },
        isShow: {
            type: Boolean,
            default: false
        },
    },

    data: function () {
        return {
            formData: {},
            speechToTextObj: [{
                resourceUrl:"",
                text:"",
                fileName:""
            }],
            loadingSave: false,
        }
    },
    created() {
        this.formData = this.deepCopy(this.editInfo)
    },
    mounted() {
      this.querySoundToText()
    },
    methods: {
        isAudio(filePath){
            let path = filePath.split("?")[0];
            return path.includes('.mp3')
                || path.includes('.wav')
                || path.includes('.flac')
                || path.includes('.aac')
                || path.includes('.m4a')
                || path.includes('.wma')
                || path.includes('.ogg')
        },
        isVideo(filePath){
            let path = filePath.split("?")[0];
            return path.includes('.mp4')
                || path.includes('.avi')
                || path.includes('.mov')
                || path.includes('.wmv')
                || path.includes('.flv')
                || path.includes('.mkv')
                || path.includes('.webm')
        },
        querySoundToText(){
          let param = {
            url: 'sound/querySoundToText',
            method: 'POST',
            data: this.formData
          }
          this.$http(param).then((res) => {
            if (res.success) {
              this.speechToTextObj = res.data.data
          // this.speechToTextObj[0].resourceUrl = "http://file.40017.cn/workweixinjsapi/voice/1.mov"
            } else {
              this.$message.error(res.msg)
            }
          }).catch(err => {
            console.log(err);
            this.$message.error('查询语音转文字失败')
          })
        },
        submitClick() {
            this.$refs.formData.validate((valid) => {
                if (this.formData.uploadStatus !== 2 && this.formData.uploadStatus !== 3){
                  valid = false
                  this.$message.error('请选择上传是否合格')
                }
                if (valid) {
                    this.loadingSave = true
                    let param = {
                        url: 'sound/audit',
                        method: 'POST',
                        data: this.formData
                    }
                    this.$http(param).then((res) => {
                        if (res.success) {
                            this.$message.success('保存成功')
                            this.$emit('callbackclose', 'success')
                        } else {
                            this.$message.error(res.msg)
                        }
                        this.loadingSave = false

                    }).catch(err => {
                        console.log(err);
                        this.$message.error('保存失败')
                        this.loadingSave = false
                    })
                }
            });

        },
        //取消
        cancelClick() {
            this.$emit('callbackclose')
        }
    }
}
</script>

<style scoped>
</style>
