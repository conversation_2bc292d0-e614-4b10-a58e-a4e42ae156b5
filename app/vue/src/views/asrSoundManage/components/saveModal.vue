<template>
    <el-dialog class="edit_pop_div" :visible="isShow" :title="'审核'"
               :close-on-click-modal="false" @close="cancelClick"
               :append-to-body="true" width="50%" center>
        <el-form label-width="140px"
                 :model="formData"
                 size="small"
                 ref="formData"
                 >
            <el-form-item label="文件">
                <el-card class="box-card">
                    <div v-for="(textObj,index) in speechToTextObj" class="text item" :key="index">
                        <template>
                            <span v-if="isAudio(textObj.resourceUrl)">
                                {{speechToTextObj.indexOf(textObj) + 1 + '号音频文件：  ' }}
                                <br>
                                下载：<a :href="textObj.resourceUrl" >  {{textObj.fileName}}</a>
                                <br>
                                预览：<audio controls ref="audio">
                                    <source :src="textObj.resourceUrl" />
                                </audio>
                                <br>
                                {{'音频文件内容：' + textObj.text}}
                            </span>
                            <span v-if="isVideo(textObj.resourceUrl)">
                                {{speechToTextObj.indexOf(textObj) + 1 + '号视频文件：' }}
                                <br>
                                下载：<a :href="textObj.resourceUrl" >{{textObj.fileName}}</a>
                                <br>
                                预览：
                                <div class="video-container">
                                    <!-- 显示加载状态 -->
                                    <div v-if="videoLoadingStates[index]" class="video-loading">
                                        <div class="loading-spinner"></div>
                                        <p>正在加载视频...</p>
                                    </div>

                                    <!-- 原生视频播放器 (非.avi格式) -->
                                    <video
                                        v-if="!isAviFormat(textObj.resourceUrl) && !videoLoadingStates[index]"
                                        :id="'video-player-' + index"
                                        :ref="'videoPlayer' + index"
                                        controls
                                        preload="metadata"
                                        width="320"
                                        height="240"
                                        :poster="defaultPoster"
                                        @loadstart="onVideoLoadStart(index)"
                                        @error="onVideoError(index, textObj)"
                                        @canplay="onVideoCanPlay(index)"
                                        @loadedmetadata="onVideoLoadedMetadata(index)">
                                        <source :src="textObj.resourceUrl" :type="getVideoMimeType(textObj.resourceUrl)"/>
                                        <p>您的浏览器不支持视频播放</p>
                                    </video>

                                    <!-- .avi格式专用播放器 -->
                                    <div
                                        v-if="isAviFormat(textObj.resourceUrl) && !videoLoadingStates[index]"
                                        :id="'avi-player-' + index"
                                        class="avi-player-container">
                                        <div class="avi-player-wrapper">
                                            <canvas
                                                :id="'avi-canvas-' + index"
                                                width="320"
                                                height="240"
                                                class="avi-canvas"
                                                @click="toggleAviPlayback(index, textObj)">
                                            </canvas>
                                            <div class="avi-controls">
                                                <button
                                                    @click="toggleAviPlayback(index, textObj)"
                                                    class="avi-play-btn"
                                                    :disabled="!aviPlayersReady[index]">
                                                    {{ aviPlayersPlaying[index] ? '⏸️' : '▶️' }}
                                                </button>
                                                <span class="avi-time">
                                                    {{ formatTime(aviCurrentTimes[index] || 0) }} / {{ formatTime(aviDurations[index] || 0) }}
                                                </span>
                                                <div class="avi-progress-container" @click="seekAviVideo($event, index)">
                                                    <div class="avi-progress-bar">
                                                        <div
                                                            class="avi-progress-fill"
                                                            :style="{ width: getAviProgress(index) + '%' }">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-if="!aviPlayersReady[index]" class="avi-loading">
                                            <p>正在初始化 AVI 播放器...</p>
                                            <button @click="initAviPlayer(index, textObj)" class="retry-btn">
                                                重新加载
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 错误状态显示 -->
                                    <div v-if="videoErrors[index]" class="video-error">
                                        <div class="error-container">
                                            <p style="color: #f56c6c; margin: 5px 0;">
                                                ❌ 视频播放失败
                                            </p>
                                            <p style="font-size: 12px; color: #999; margin: 5px 0;">
                                                该视频格式可能不被支持，请下载后使用本地播放器观看
                                            </p>
                                            <div class="error-actions">
                                                <a :href="textObj.resourceUrl" download class="download-btn">
                                                    📥 下载视频
                                                </a>
                                                <button @click="retryVideoLoad(index, textObj)" class="retry-btn">
                                                    🔄 重试
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <br>
                                {{'视频文件内容：' + textObj.text}}
                            </span>
                        </template>
                        <el-divider v-if="speechToTextObj.indexOf(textObj) < speechToTextObj.length - 1"/>
                    </div>
                </el-card>
            </el-form-item>
            <el-form-item label="检查员司机服务">
                <span style="color: #3a8ee6">{{formData.serviceTag}}</span>
            </el-form-item>
            <el-form-item label="检查员点评">
                <span style="color: #3a8ee6">{{formData.serviceReview}}</span>
            </el-form-item>
            <el-form-item label="检查员评分">
                <el-input-number :min="0" :max="5" :precision="2" :step="0.1" v-model="formData.ratingScore" disabled></el-input-number>
            </el-form-item>
            <el-form-item label="审核意见" prop="auditComment">
                <el-input type="textarea" v-model="formData.auditComment" maxlength="256" :autosize="{minRows: 5, maxRows: 10}"></el-input>
            </el-form-item>

            <el-form-item label="上传是否合格" prop="uploadStatus">
              <el-radio v-model="formData.uploadStatus" :label="2">不合格</el-radio>
              <el-radio v-model="formData.uploadStatus" :label="3">合格</el-radio>
            </el-form-item>

          <el-form-item label="司机是否违规" prop="driverViolated">
            <el-radio v-model="formData.driverViolated" :label="1">违规</el-radio>
            <el-radio v-model="formData.driverViolated" :label="0">不违规</el-radio>
          </el-form-item>

          <el-form-item label="司机服务评分" prop="driverScore">
            <el-input-number :min="0" :max="5" :precision="2" :step="0.1" v-model="formData.driverScore"></el-input-number>
          </el-form-item>

        </el-form>

        <span slot="footer" class="dialog-footer">
       <el-button type="default" size="mini" @click="cancelClick">
            取消
        </el-button>
        <el-button type="primary" :loading="loadingSave" size="mini" @click="submitClick">
            保存
        </el-button>
    </span>
    </el-dialog>

</template>

<script>
export default {
    props: {
        editInfo: {
            type: Object,
            default() {
                return {}
            }
        },
        isShow: {
            type: Boolean,
            default: false
        },
    },

    data: function () {
        return {
            formData: {},
            speechToTextObj: [{
                resourceUrl:"",
                text:"",
                fileName:""
            }],
            loadingSave: false,
            videoErrors: {},
            videoLoadingStates: {},
            defaultPoster: "//pic5.40017.cn/i/ori/PS2lfS0492.jpg",
            videoJsPlayers: {},
            // AVI 播放器相关状态
            aviPlayersReady: {},
            aviPlayersPlaying: {},
            aviCurrentTimes: {},
            aviDurations: {},
            aviPlayers: {},
            aviIntervals: {},
            jsmpegLoaded: false,
        }
    },
    created() {
        this.formData = this.deepCopy(this.editInfo)
    },
    mounted() {
      this.querySoundToText()
      this.loadVideoLibraries()
    },
    beforeDestroy() {
        // 清理所有播放器实例
        this.cleanupAllPlayers()
    },
    watch: {
        speechToTextObj: {
            handler(newVal) {
                if (newVal && newVal.length > 0) {
                    this.$nextTick(() => {
                        this.initializeVideoPlayers()
                    })
                }
            },
            immediate: true
        }
    },
    methods: {
        isAudio(filePath){
            let path = filePath.split("?")[0];
            return path.includes('.mp3')
                || path.includes('.wav')
                || path.includes('.flac')
                || path.includes('.aac')
                || path.includes('.m4a')
                || path.includes('.wma')
                || path.includes('.ogg')
        },
        isVideo(filePath){
            let path = filePath.split("?")[0];
            return path.includes('.mp4')
                || path.includes('.avi')
                || path.includes('.mov')
                || path.includes('.wmv')
                || path.includes('.flv')
                || path.includes('.mkv')
                || path.includes('.webm')
        },
        getVideoMimeType(filePath) {
            let path = filePath.split("?")[0].toLowerCase();
            if (path.includes('.mp4')) return 'video/mp4';
            if (path.includes('.avi')) return 'video/x-msvideo';
            if (path.includes('.mov')) return 'video/quicktime';
            if (path.includes('.wmv')) return 'video/x-ms-wmv';
            if (path.includes('.flv')) return 'video/x-flv';
            if (path.includes('.mkv')) return 'video/x-matroska';
            if (path.includes('.webm')) return 'video/webm';
            if (path.includes('.ogg')) return 'video/ogg';
            return 'video/mp4'; // 默认
        },
        isAviFormat(filePath) {
            let path = filePath.split("?")[0].toLowerCase();
            return path.includes('.avi');
        },
        loadVideoLibraries() {
            // 加载 JSMpeg 用于 AVI 播放
            this.loadJSMpeg();
        },
        loadJSMpeg() {
            if (typeof window.JSMpeg === 'undefined' && !this.jsmpegLoaded) {
                this.jsmpegLoaded = true;
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/jsmpeg@0.2.0/jsmpeg.min.js';
                script.onload = () => {
                    console.log('JSMpeg loaded successfully');
                };
                script.onerror = () => {
                    console.error('Failed to load JSMpeg');
                    this.jsmpegLoaded = false;
                };
                document.head.appendChild(script);
            }
        },
        initializeVideoPlayers() {
            this.speechToTextObj.forEach((textObj, index) => {
                if (this.isVideo(textObj.resourceUrl)) {
                    if (this.isAviFormat(textObj.resourceUrl)) {
                        this.$set(this.aviPlayersReady, index, false);
                        this.$set(this.aviPlayersPlaying, index, false);
                        this.$set(this.aviCurrentTimes, index, 0);
                        this.$set(this.aviDurations, index, 0);
                        this.initAviPlayer(index, textObj);
                    }
                }
            });
        },
        onVideoLoadStart(index) {
            console.log(`Video ${index} loading started`);
            this.$set(this.videoLoadingStates, index, true);
        },
        onVideoLoadedMetadata(index) {
            console.log(`Video ${index} metadata loaded`);
            this.$set(this.videoLoadingStates, index, false);
        },
        onVideoCanPlay(index) {
            console.log(`Video ${index} can play`);
            this.$set(this.videoErrors, index, false);
            this.$set(this.videoLoadingStates, index, false);
        },
        onVideoError(index, textObj) {
            console.log(`Video ${index} error`);
            this.$set(this.videoErrors, index, true);
            this.$set(this.videoLoadingStates, index, false);
        },
        retryVideoLoad(index, textObj) {
            this.$set(this.videoErrors, index, false);
            this.$set(this.videoLoadingStates, index, true);

            // 重新加载视频
            this.$nextTick(() => {
                if (this.isAviFormat(textObj.resourceUrl)) {
                    this.initAviPlayer(index, textObj);
                } else {
                    const videoElement = this.$refs[`videoPlayer${index}`];
                    if (videoElement && videoElement[0]) {
                        videoElement[0].load();
                    }
                }
            });
        },
        // AVI 播放器相关方法
        initAviPlayer(index, textObj) {
            this.$set(this.videoLoadingStates, index, true);

            // 检查 JSMpeg 是否已加载
            const checkJSMpeg = () => {
                if (typeof window.JSMpeg !== 'undefined') {
                    this.createAviPlayer(index, textObj);
                } else {
                    // 等待 JSMpeg 加载
                    setTimeout(checkJSMpeg, 100);
                }
            };

            checkJSMpeg();
        },
        createAviPlayer(index, textObj) {
            try {
                const canvasId = `avi-canvas-${index}`;
                const canvas = document.getElementById(canvasId);

                if (!canvas) {
                    console.error(`Canvas ${canvasId} not found`);
                    this.$set(this.videoLoadingStates, index, false);
                    this.$set(this.videoErrors, index, true);
                    return;
                }

                // 创建一个简单的 AVI 播放器
                this.createSimpleAviPlayer(index, textObj, canvas);

            } catch (error) {
                console.error('Error creating AVI player:', error);
                this.$set(this.videoLoadingStates, index, false);
                this.$set(this.videoErrors, index, true);
            }
        },
        createSimpleAviPlayer(index, textObj, canvas) {
            // 创建一个隐藏的 video 元素来尝试播放
            const hiddenVideo = document.createElement('video');
            hiddenVideo.style.display = 'none';
            hiddenVideo.crossOrigin = 'anonymous';
            hiddenVideo.preload = 'metadata';

            // 添加多种源格式
            const sources = [
                { src: textObj.resourceUrl, type: 'video/x-msvideo' },
                { src: textObj.resourceUrl, type: 'video/avi' },
                { src: textObj.resourceUrl, type: 'video/mp4' },
                { src: textObj.resourceUrl, type: '' } // 让浏览器自动检测
            ];

            sources.forEach(source => {
                const sourceElement = document.createElement('source');
                sourceElement.src = source.src;
                sourceElement.type = source.type;
                hiddenVideo.appendChild(sourceElement);
            });

            document.body.appendChild(hiddenVideo);

            const ctx = canvas.getContext('2d');

            // 绘制默认画面
            this.drawDefaultFrame(ctx, canvas.width, canvas.height);

            hiddenVideo.addEventListener('loadedmetadata', () => {
                console.log(`AVI video ${index} metadata loaded`);
                this.$set(this.aviDurations, index, hiddenVideo.duration || 0);
                this.$set(this.aviPlayersReady, index, true);
                this.$set(this.videoLoadingStates, index, false);
                this.$set(this.videoErrors, index, false);

                // 绘制第一帧
                this.drawVideoFrame(hiddenVideo, ctx, canvas.width, canvas.height);
            });

            hiddenVideo.addEventListener('timeupdate', () => {
                this.$set(this.aviCurrentTimes, index, hiddenVideo.currentTime);
                if (this.aviPlayersPlaying[index]) {
                    this.drawVideoFrame(hiddenVideo, ctx, canvas.width, canvas.height);
                }
            });

            hiddenVideo.addEventListener('ended', () => {
                this.$set(this.aviPlayersPlaying, index, false);
                if (this.aviIntervals[index]) {
                    clearInterval(this.aviIntervals[index]);
                    delete this.aviIntervals[index];
                }
            });

            hiddenVideo.addEventListener('error', (e) => {
                console.error(`AVI video ${index} error:`, e);
                this.$set(this.videoLoadingStates, index, false);
                this.$set(this.videoErrors, index, true);
                this.$set(this.aviPlayersReady, index, false);
                document.body.removeChild(hiddenVideo);
            });

            // 保存播放器引用
            this.aviPlayers[index] = hiddenVideo;

            // 尝试加载视频
            hiddenVideo.load();
        },
        drawDefaultFrame(ctx, width, height) {
            // 绘制默认背景
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, width, height);

            // 绘制播放图标
            ctx.fillStyle = '#fff';
            ctx.font = '48px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('▶', width / 2, height / 2);
        },
        drawVideoFrame(video, ctx, width, height) {
            try {
                ctx.drawImage(video, 0, 0, width, height);
            } catch (error) {
                // 如果绘制失败，显示默认画面
                this.drawDefaultFrame(ctx, width, height);
            }
        },
        toggleAviPlayback(index, textObj) {
            const player = this.aviPlayers[index];
            if (!player || !this.aviPlayersReady[index]) {
                this.initAviPlayer(index, textObj);
                return;
            }

            if (this.aviPlayersPlaying[index]) {
                player.pause();
                this.$set(this.aviPlayersPlaying, index, false);
                if (this.aviIntervals[index]) {
                    clearInterval(this.aviIntervals[index]);
                    delete this.aviIntervals[index];
                }
            } else {
                player.play().then(() => {
                    this.$set(this.aviPlayersPlaying, index, true);
                }).catch(error => {
                    console.error('Play failed:', error);
                    this.$set(this.videoErrors, index, true);
                });
            }
        },
        seekAviVideo(event, index) {
            const player = this.aviPlayers[index];
            if (!player || !this.aviPlayersReady[index]) return;

            const progressContainer = event.currentTarget;
            const rect = progressContainer.getBoundingClientRect();
            const clickX = event.clientX - rect.left;
            const percentage = clickX / rect.width;
            const newTime = percentage * this.aviDurations[index];

            player.currentTime = newTime;
            this.$set(this.aviCurrentTimes, index, newTime);
        },
        getAviProgress(index) {
            const duration = this.aviDurations[index] || 0;
            const currentTime = this.aviCurrentTimes[index] || 0;
            return duration > 0 ? (currentTime / duration) * 100 : 0;
        },
        formatTime(seconds) {
            if (!seconds || isNaN(seconds)) return '0:00';
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        },
        cleanupAllPlayers() {
            // 清理 Video.js 实例
            Object.keys(this.videoJsPlayers).forEach(key => {
                if (this.videoJsPlayers[key] && typeof this.videoJsPlayers[key].dispose === 'function') {
                    this.videoJsPlayers[key].dispose();
                }
            });

            // 清理 AVI 播放器
            Object.keys(this.aviPlayers).forEach(key => {
                const player = this.aviPlayers[key];
                if (player && player.parentNode) {
                    player.pause();
                    player.parentNode.removeChild(player);
                }
            });

            // 清理定时器
            Object.keys(this.aviIntervals).forEach(key => {
                clearInterval(this.aviIntervals[key]);
            });
        },
        querySoundToText(){
          let param = {
            url: 'sound/querySoundToText',
            method: 'POST',
            data: this.formData
          }
          this.$http(param).then((res) => {
            if (res.success) {
              this.speechToTextObj = res.data.data
          // this.speechToTextObj[0].resourceUrl = "http://file.40017.cn/workweixinjsapi/voice/1.mov"
            } else {
              this.$message.error(res.msg)
            }
          }).catch(err => {
            console.log(err);
            this.$message.error('查询语音转文字失败')
          })
        },
        submitClick() {
            this.$refs.formData.validate((valid) => {
                if (this.formData.uploadStatus !== 2 && this.formData.uploadStatus !== 3){
                  valid = false
                  this.$message.error('请选择上传是否合格')
                }
                if (valid) {
                    this.loadingSave = true
                    let param = {
                        url: 'sound/audit',
                        method: 'POST',
                        data: this.formData
                    }
                    this.$http(param).then((res) => {
                        if (res.success) {
                            this.$message.success('保存成功')
                            this.$emit('callbackclose', 'success')
                        } else {
                            this.$message.error(res.msg)
                        }
                        this.loadingSave = false

                    }).catch(err => {
                        console.log(err);
                        this.$message.error('保存失败')
                        this.loadingSave = false
                    })
                }
            });

        },
        //取消
        cancelClick() {
            this.$emit('callbackclose')
        },
        // 深拷贝方法（如果不存在的话）
        deepCopy(obj) {
            if (obj === null || typeof obj !== 'object') return obj;
            if (obj instanceof Date) return new Date(obj.getTime());
            if (obj instanceof Array) return obj.map(item => this.deepCopy(item));
            if (typeof obj === 'object') {
                const copy = {};
                Object.keys(obj).forEach(key => {
                    copy[key] = this.deepCopy(obj[key]);
                });
                return copy;
            }
        }
    }
}
</script>

<style scoped>
.video-container {
    margin: 10px 0;
}

.video-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 320px;
    height: 240px;
    border: 1px solid #ddd;
    background-color: #f5f5f5;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #409EFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.video-error {
    margin-top: 10px;
}

.error-container {
    border: 1px dashed #f56c6c;
    padding: 15px;
    text-align: center;
    width: 320px;
    background-color: #fef0f0;
    border-radius: 4px;
}

.error-actions {
    margin-top: 10px;
    display: flex;
    gap: 10px;
    justify-content: center;
}

.download-btn, .retry-btn {
    padding: 5px 10px;
    border: 1px solid #409EFF;
    background-color: #409EFF;
    color: white;
    text-decoration: none;
    border-radius: 3px;
    font-size: 12px;
    cursor: pointer;
}

.download-btn:hover, .retry-btn:hover {
    background-color: #66b1ff;
}

.retry-btn {
    background-color: #67c23a;
    border-color: #67c23a;
}

.retry-btn:hover {
    background-color: #85ce61;
}

/* AVI 播放器样式 */
.avi-player-container {
    width: 320px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    background-color: #000;
}

.avi-player-wrapper {
    position: relative;
}

.avi-canvas {
    display: block;
    width: 100%;
    height: auto;
    cursor: pointer;
    background-color: #000;
}

.avi-controls {
    display: flex;
    align-items: center;
    padding: 8px;
    background-color: #333;
    color: white;
    font-size: 12px;
}

.avi-play-btn {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    margin-right: 10px;
    padding: 4px 8px;
    border-radius: 3px;
}

.avi-play-btn:hover {
    background-color: #555;
}

.avi-play-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.avi-time {
    margin-right: 10px;
    white-space: nowrap;
}

.avi-progress-container {
    flex: 1;
    height: 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.avi-progress-bar {
    width: 100%;
    height: 4px;
    background-color: #666;
    border-radius: 2px;
    overflow: hidden;
}

.avi-progress-fill {
    height: 100%;
    background-color: #409EFF;
    transition: width 0.1s ease;
}

.avi-loading {
    padding: 20px;
    text-align: center;
    background-color: #f5f5f5;
    color: #666;
}

.avi-loading p {
    margin: 0 0 10px 0;
}
</style>
