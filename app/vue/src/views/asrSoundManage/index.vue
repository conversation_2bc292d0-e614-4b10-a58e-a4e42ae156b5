<template>
    <div v-cloak class="custom-content-wrapper">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline" size="mini" label-width="130px">

            <el-row>
              <el-col :span="6">
                <el-form-item label="上传状态">
                  <el-select v-model="searchForm.uploadStatus" placeholder="请选择">
                    <el-option label="全部" :value="''"></el-option>
                    <el-option label="待上传" :value="0"></el-option>
                    <el-option label="已上传" :value="1"></el-option>
                    <el-option label="不合格" :value="2"></el-option>
                    <el-option label="合格" :value="3"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
                <el-col :span="6">
                    <el-form-item label="车牌号">
                        <el-input v-model="searchForm.vehicleNo" clearable maxlength="10"></el-input>
                    </el-form-item>
                </el-col>
              <el-col :span="6">
                <el-form-item label="订单ID">
                  <el-input v-model="searchForm.orderSerialNo" maxlength="64" style="width: 150%" clearable></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
                <el-col :span="6">
                    <el-form-item label="审核状态">
                        <el-select v-model="searchForm.auditStatus" placeholder="请选择">
                            <el-option label="全部" :value="''"></el-option>
                            <el-option label="待审核" :value="0"></el-option>
                            <el-option label="已审核" :value="1"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="手机号">
                        <el-input v-model="searchForm.mobileNo" clearable>
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="时间">
                        <el-date-picker
                                v-model="createTimeRange"
                                filterable
                                @change="handlerChange"
                                type="datetimerange"
                                align="right"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="6">
                    <el-form-item label="产品类型">
                        <el-select v-model="searchForm.productType" placeholder="请选择">
                            <el-option label="全部" :value="''"></el-option>
                            <el-option label="预约专车" :value="11"></el-option>
                            <el-option label="及时专车" :value="19"></el-option>
                            <el-option label="接机" :value="12"></el-option>
                            <el-option label="送机" :value="13"></el-option>
                            <el-option label="接站" :value="14"></el-option>
                            <el-option label="送站" :value="15"></el-option>
                            <el-option label="顺风车" :value="80"></el-option>
                            <el-option label="上门接送" :value="90"></el-option>
                            <el-option label="上门接送-接机" :value="91"></el-option>
                            <el-option label="上门接送-送机" :value="92"></el-option>
                            <el-option label="上门接送-接站" :value="93"></el-option>
                            <el-option label="上门接送-送站" :value="94"></el-option>
                            <el-option label="国际接机" :value="32"></el-option>
                            <el-option label="国际送机" :value="33"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row type="flex" justify="center" style="margin-bottom: 10px">
                <div style="margin-left: 10px">
                    <el-button size="mini" plain icon="el-icon-refresh" @click="resetClick">重置</el-button>
                </div>
                <div style="margin-left: 10px">
                    <el-button size="mini" type="primary" plain icon="el-icon-search" @click="queryClick">查询</el-button>
                </div>
            </el-row>
        </el-form>

        <tablePage ref="multipleTable" :request-param="{
                url: 'sound/queryPage',
                method: 'POST',
                data: this.searchForm
            }">
            <el-table-column prop="orderSerialNo" align="center" label="订单ID"></el-table-column>
            <el-table-column prop="orderTypeDesc" align="center" label="产品类型"></el-table-column>
            <el-table-column prop="mobileNo" align="center" label="手机号"></el-table-column>
            <el-table-column align="center" label="行程时间">
                <template slot-scope="{row}">
                    <span>{{row.tripStartTime | formatDate}}</span>
                    <br>
                    <span>----</span>
                    <br>
                    <span>{{row.tripEndTime | formatDate}}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" label="上传时间">
                <template slot-scope="{row}">
                    <span v-if="row.uploadStatus !== 0">{{row.uploadTime | formatDate}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="plateNo" align="center" label="车牌号"></el-table-column>
            <el-table-column align="center" label="上传状态">
                <template slot-scope="{row}">
                    <span v-if="row.uploadStatus === 0">待上传</span>
                    <span v-if="row.uploadStatus === 1">已上传</span>
                    <span v-if="row.uploadStatus === 2">不合格</span>
                    <span v-if="row.uploadStatus === 3">合格</span>
                </template>
            </el-table-column>
            <el-table-column align="center" label="审核状态">
                <template slot-scope="{row}">
                    <span v-if="row.auditStatus === 0">待审核</span>
                    <span v-if="row.auditStatus === 1">已审核</span>
                </template>
            </el-table-column>
            <el-table-column align="center" label="操作时间">
                <template slot-scope="{row}">
                    <span>{{row.updateTime | formatDate}}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="180" fixed="right">
                <template slot-scope="{row}">
                    <el-button type="primary" size="mini" plain icon="el-icon-download" @click="downloadFile(row)" v-if="row.fileUrl && row.fileUrl !== ''">下载</el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-edit" @click="editClick(row)" v-if="row.uploadStatus !== 0 && row.auditStatus !== 1">审核</el-button>
                </template>
            </el-table-column>
        </tablePage>
        <saveModal :edit-info="editInfo" :is-show="showSaveModal" v-if="showSaveModal" @callbackclose="closeSaveModal"></saveModal>

    </div>
</template>


<script>
import saveModal from './components/saveModal'
import tablePage from "@/components/tablePage";
import {parseTime} from "@/utils";

let orgSearchForm = {
    orderSerialNo: '',
    vehicleNo: '',
    uploadStatus: '',
    auditStatus: '',
    mobileNo: '',
    startTime: '',
    endTime: '',
    productType: '',
}


export default {
    components: {
        saveModal,
        tablePage
    },
    data() {
        return {
            total: 1,
            tableData:[],
            createTimeRange: [],
            listQuery: {
                page: 1,
                limit: 10
            },
            searchForm: this.deepCopy(orgSearchForm),
            editInfo: {},
            showSaveModal: false
        }
    },
    created() {
        this.setDefaultTime()
    },
    mounted() {
        this.queryClick()
    },
    filters: {
        formatDate: function(value) {
            if (value == null) {
                return ''
            }
            let date = new Date(value)
            let y = date.getFullYear()
            let MM = date.getMonth() + 1
            MM = MM < 10 ? ('0' + MM) : MM
            let d = date.getDate()
            d = d < 10 ? ('0' + d) : d
            let h = date.getHours()
            h = h < 10 ? ('0' + h) : h
            let m = date.getMinutes()
            m = m < 10 ? ('0' + m) : m
            let s = date.getSeconds()
            s = s < 10 ? ('0' + s) : s
            return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s
        }
    },
    methods: {
        // downloadFile(row){
        //   const fileUri = row.fileUrl
        //   // 创建临时 a 标签进行下载
        //   const link = document.createElement('a');
        //   link.href = fileUri;
        //   link.target = '_blank';
        //   link.style.display = 'none';
        //   document.body.appendChild(link);
        //   link.click();
        //   document.body.removeChild(link);
        // },
        downloadFile(row){
          debugger;
          const fileUri = row.fileUrl;
          // 使用 fetch 获取文件并触发下载
          fetch(fileUri)
              .then(response => response.blob())
              .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = this.getFileNameFromUrl(fileUri); // 设置文件名
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
              })
              .catch(error => {
                console.error('下载失败:', error);
                this.$message.error('文件下载失败');
              });
        },

        // 从URL中提取文件名
        getFileNameFromUrl(url) {
          const fileName = url.split('/').pop().split('?')[0];
          return decodeURIComponent(fileName) || 'download';
        },
        handlerChange(val) {

        },
        setDefaultTime() {
            let now = new Date();
            let startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 30, 0, 0, 0);
            let endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
            this.createTimeRange = [startOfDay, endOfDay];
        },
        //重置
        resetClick() {
            this.searchForm = this.deepCopy(orgSearchForm)
            this.setDefaultTime()
        },
        //查询
        queryClick() {
            if (this.createTimeRange && this.createTimeRange.length > 0){
              this.searchForm.startTime = parseTime(this.createTimeRange[0],'{y}-{m}-{d} {h}:{i}:{s}')
              this.searchForm.endTime = parseTime(this.createTimeRange[1],'{y}-{m}-{d} {h}:{i}:{s}')
            }else {
              this.searchForm.startTime = ''
              this.searchForm.endTime = ''
            }
            this.$refs.multipleTable.searchData(1)
        },
        editClick(item) {
            this.editInfo = this.deepCopy(item)
            this.showSaveModal = true
        },
        closeSaveModal(result) {
            this.showSaveModal = false
            if (result) {
                setTimeout(()=>{
                    this.queryClick()
                },500)
            }
        }
    }
};
</script>

<style>
</style>
