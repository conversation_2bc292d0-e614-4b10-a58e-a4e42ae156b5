<template>
    <div v-cloak class="custom-content-wrapper">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline" size="mini" label-width="130px">

            <el-form-item label="策略编号">
                <el-input v-model="searchForm.strategyNo" clearable placeholder="请输入策略编号"></el-input>
            </el-form-item>
            <el-form-item label="策略名称">
                <el-input v-model="searchForm.name" clearable placeholder="请输入策略名称">
                </el-input>
            </el-form-item>
            <el-form-item label="风险类型">
                <selectRiskType v-model="searchForm.riskType"></selectRiskType>
            </el-form-item>
            <el-form-item label="状态">
                <selectStatus v-model="searchForm.status"></selectStatus>
            </el-form-item>

            <el-form-item label="操作人">
                <el-input v-model="searchForm.updateBy" clearable placeholder="请输入操作人">
                </el-input>
            </el-form-item>
            <el-form-item label="业务线">
                <selectProductLine :is-multiple="true" v-model="searchForm.productLineList"></selectProductLine>
            </el-form-item>
            <el-row type="flex" justify="center" style="margin-bottom: 10px">
                <div style="margin-left: 10px">
                    <el-button size="mini" plain icon="el-icon-refresh" @click="resetClick">重置
                    </el-button>
                </div>
                <div style="margin-left: 10px">
                    <el-button size="mini" type="primary" plain icon="el-icon-search" @click="queryClick">查询
                    </el-button>
                </div>
            </el-row>
        </el-form>

        <el-row style="margin-bottom: 10px">
            <el-button size="mini" type="primary" plain icon="el-icon-plus" @click="addClick">新建策略
            </el-button>
        </el-row>

        <tablePage ref="multipleTable" :request-param="{
                url: 'offlinemetricstrategy/queryPage',
                method: 'POST',
                data: this.searchForm
            }">
            <el-table-column prop="id" align="center" label="策略ID" width="80"/>
            <el-table-column prop="strategyNo" align="center" label="策略编号">
            </el-table-column>
            <el-table-column prop="name" align="center" label="策略名称">
            </el-table-column>
            <el-table-column prop="productLineDescs" align="center" label="业务线">
            </el-table-column>
            <el-table-column prop="fieldCount" align="center" label="关联指标数">
            </el-table-column>
            <el-table-column prop="status" align="center" label="状态">
                <template slot-scope="{row}">
                    <statusTag :status="row.status"/>
                </template>
            </el-table-column>

            <el-table-column prop="updateTime" align="center" label="更新时间">
                <template slot-scope="{row}">
                    {{ formatDate(row.updateTime) }}
                </template>
            </el-table-column>
            <el-table-column prop="updateBy" align="center" label="操作人">
            </el-table-column>
            <el-table-column align="center" label="操作" width="300" fixed="right">
                <template slot-scope="{row}">
                    <el-button type="primary" size="mini" plain icon="el-icon-edit" @click="editClick(row)">编辑
                    </el-button>
                    <permission style="margin-left: 5px" name="AsrAdmin:offlineMetricField:save">
                        <el-popconfirm title="确定要删除吗？" @confirm="deleteClick(row)">
                            <el-button type="danger" icon="el-icon-delete" slot="reference" size="mini">删除</el-button>
                        </el-popconfirm>
                    </permission>
                    <el-button style="margin-left: 5px" size="mini" plain icon="el-icon-tickets"
                               @click="logClick(row.id)">日志
                    </el-button>
                </template>
            </el-table-column>
        </tablePage>
        <saveModal :edit-info="editInfo" :is-show="showSaveModal" v-if="showSaveModal"
                   @callbackclose="closeSaveModal"></saveModal>
        <operatorLog log-type="OFFLINE_METRIC_STRATEGY" ref="operatorLog"/>
    </div>
</template>


<script>
import saveModal from './components/saveModal'
import selectStatus from "@/components/selectStatus";
import permission from "@/components/permission";
import tablePage from "@/components/tablePage";
import operatorLog from "@/components/operatorLog";
import statusTag from "@/components/statusTag";
import selectRiskType from "@/components/selectRiskType";
import selectProductLine from "@/components/selectProductLine";

let orgSearchForm = {
    strategyNo: '',
    name: '',
    riskType: '',
    status: 1,
    updateBy: '',
    productLineList: []
}


export default {
    components: {
        saveModal,
        selectStatus,
        permission,
        tablePage,
        operatorLog,
        statusTag,
        selectRiskType,
        selectProductLine
    },
    data() {
        return {
            searchForm: this.deepCopy(orgSearchForm),
            editInfo: {},
            showSaveModal: false
        }
    },

    created() {
    },
    mounted() {
        this.queryClick()
    },
    filters: {},
    methods: {
        //重置
        resetClick() {
            this.searchForm = this.deepCopy(orgSearchForm)
        },
        //查询
        queryClick() {
            this.$refs.multipleTable.searchData(1)
        },
        editClick(item) {
            this.editInfo = this.deepCopy(item)
            this.showSaveModal = true
        },
        deleteClick(item) {
            let param = {
                url: 'offlinemetricstrategy/save',
                method: 'POST',
                data: {
                    id: item.id,
                    status: -1,
                    checkExists: false
                }
            }
            this.$http(param).then((res) => {
                if (res.success) {
                    this.$message.success('删除成功')
                    this.$refs.multipleTable.searchData()
                } else {
                    this.$message.error(res.msg)
                }

            }).catch(e => {
                this.$message.error("操作失败")
                console.warn(e)
            })
        },
        addClick() {
            this.editInfo = {}
            this.showSaveModal = true
        },
        closeSaveModal(result) {
            this.showSaveModal = false
            if (result) {
                this.$refs.multipleTable.searchData()
            }
        }
    }
};
</script>

<style>
</style>
