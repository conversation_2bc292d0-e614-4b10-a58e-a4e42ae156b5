<template>
    <el-dialog class="edit_pop_div" :visible="isShow" :title="formData.id?'编辑':'新增'"
               :close-on-click-modal="false" @close="cancelClick"
               :append-to-body="true" width="60%" center>
        <el-form label-width="140px"
                 :model="formData"
                 size="small"
                 ref="formData"
        >
            <modalTitle text="基本信息">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="策略编号" prop="strategyNo"
                                      :rules="[{ required: true, message: '策略编号不能为空' }]">
                            <el-input class="w100" v-model="formData.strategyNo" maxlength="64" clearable
                                      :disabled="formData.id!==''"
                                      placeholder="请输入策略编号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="策略名称" prop="name"
                                      :rules="[{ required: true, message: '策略名称不能为空' }]">
                            <el-input class="w100" v-model="formData.name" maxlength="64" clearable
                                      placeholder="请输入策略名称"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item label="执行频率" prop="during"
                                      :rules="[{ required: true, message: '执行频率不能为空' }]">
                            <el-select class="w100" v-model="formData.during">
                                <el-option label="每小时" value="HOUR"></el-option>
                                <el-option label="每月" value="MONTH"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="城市" prop="cityId">
                            <selectCity v-model="formData.cityId" text="所有"></selectCity>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item label="状态" prop="status" :rules="[{ required: true, message: '状态不能为空'}]">
                            <selectStatus v-model="formData.status"></selectStatus>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="风险类型" prop="riskType"
                                      :rules="[{ required: true, message: '风险类型不能为空'}]">
                            <selectRiskType v-model="formData.riskType"></selectRiskType>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item label="渠道" prop="channelList"
                        >
                            <selectOrderChannel text="所有" :is-multiple="true"
                                                v-model="formData.channelList"></selectOrderChannel>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="供应商" prop="channelList">
                            <selectSupplier text="所有" :is-remote="false" :is-multiple="true"
                                            v-model="formData.supplierCodeList"></selectSupplier>
                        </el-form-item>
                    </el-col>
                </el-row>


                <el-form-item label="策略说明" prop="description">
                    <el-input type="textarea" rows="3" show-word-limit v-model="formData.description"
                              maxlength="256" clearable placeholder="请输入策略说明"></el-input>
                </el-form-item>
            </modalTitle>
            <modalTitle text="命中管控">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="选择业务线" prop="productLineList"
                                      :rules="[{ required: true, message: '业务线不能为空'}]">
                            <selectProductLine :is-multiple="true"
                                               v-model="formData.productLineList"></selectProductLine>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="策略文案" prop="strategyWord">
                            <el-input class="w100" v-model="formData.strategyWord" maxlength="128" clearable
                                      placeholder="请输入策略文案"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>


            </modalTitle>
            <modalTitle text="处置策略">

                <el-row>
                    <el-col :span="12">
                        <el-form-item label="策略表达式" prop="expression"
                                      :rules="[{ required: true, message: '策略表达式不能为空'}]">
                            <el-input class="w100" v-model="formData.expression" maxlength="128" clearable
                                      placeholder="请输入策略表达式,由&|()和指标序号组成"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="处置动作" prop="disposeAction"
                                      :rules="[{ required: true, message: '处置动作不能为空'}]">
                            <el-select class="w100" v-model="formData.disposeAction">
                                <el-option label="禁止" :value="0"/>
                                <el-option label="增强校验" :value="1"/>
                                <el-option label="通过" :value="2"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>


                <el-table :data="formData.relationList" :highlight-current-row="true" border stripe>
                    <el-table-column prop="sort" label="序号" width="80" align="center"></el-table-column>
                    <el-table-column prop="fieldNo" label="指标编号" width="120" align="center"></el-table-column>
                    <el-table-column label="指标内容" align="center">
                        <template slot-scope="scope">
                            <el-form-item :prop="`relationList.${scope.$index}.fieldId`" label-width="0"
                                          :rules="[{ required: true, message: '指标内容不能为空'}]">
                                <el-select clearable filterable style="width: 100%" v-model="scope.row.fieldId"
                                           @change="val=>fieldChange(scope.row)">
                                    <el-option v-for="(son) in fieldList" :label="son.name" :value="son.id"/>
                                </el-select>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column label="运算符" align="center" width="150">
                        <template slot-scope="scope">
                            <el-form-item :prop="`relationList.${scope.$index}.operator`" label-width="0"
                                          :rules="[{ required: true, message: '运算符不能为空'}]">
                                <el-select v-model="scope.row.operator" clearable>
                                    <el-option label="大于" value=">"/>
                                    <el-option label="小于" value="<"/>
                                    <el-option label="等于" value="=="/>
                                    <el-option label="小于等于" value="<="/>
                                    <el-option label="大于等于" value=">="/>
                                    <el-option label="不等于" value="!="/>
                                </el-select>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column label="阈值" width="130">
                        <template slot-scope="scope">
                            <el-select v-model="scope.row.rightType" style="margin-bottom: 5px">
                                <el-option label="常量" :value="0"/>
                            </el-select>
                            <el-form-item :prop="`relationList.${scope.$index}.rightValue`" label-width="0"
                                          :rules="[{ required: true, message: '阈值不能为空'}]">
                                <el-input type="number" @keydown.native="clearScientificInput" v-model="scope.row.rightValue" maxlength="64" clearable
                                          placeholder="请输入阈值"></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column prop="operatorFormatTime" label="操作" width="100">
                        <template slot-scope="scope">
                            <el-row style="margin-bottom: 5px">
                                <el-button type="primary" icon="el-icon-plus" size="mini" @click="addRelation">新增
                                </el-button>
                            </el-row>
                            <el-row>
                                <el-button v-if="formData.relationList.length>1" type="danger" icon="el-icon-delete"
                                           size="mini"
                                           @click="deleteRelation(scope.row)">
                                    删除
                                </el-button>
                            </el-row>
                        </template>
                    </el-table-column>
                </el-table>
            </modalTitle>
        </el-form>

        <span slot="footer" class="dialog-footer">
       <el-button type="default" size="mini" @click="cancelClick">
            取消
        </el-button>
        <el-button type="primary" :loading="loadingSave" size="mini" @click="submitClick">
            保存
        </el-button>
    </span>
    </el-dialog>

</template>

<script>

import {isEmpty} from "@/utils/util";
import selectStatus from "@/components/selectStatus";
import selectRiskType from "@/components/selectRiskType";
import selectOrderChannel from "@/components/selectOrderChannel";
import selectSupplier from "@/components/selectSupplier";
import selectProductLine from "@/components/selectProductLine";
import selectCity from "@/components/selectCity";
import modalTitle from "@/components/modalTitle";

let orgRelation = {
    id: '',
    strategyId: '',
    fieldId: '',
    operator: '',
    fieldNo: '',
    rightType: 0,
    rightValue: '',
    sort: ''
}

let orgFormData = {
    id: '',
    name: '',
    strategyNo: '',
    status: 1,
    riskType: '',
    during: '',
    description: '',
    productLines: '',
    productLineList: [],
    strategyWord: '',
    expression: '',
    script: '',
    disposeAction: '',
    channels: '',
    channelList: [],
    cityId: '',
    supplierCodes: '',
    supplierCodeList: [],
    relationList: [
        orgRelation
    ]
}

export default {
    components: {
        selectStatus,
        selectRiskType,
        selectOrderChannel,
        selectSupplier,
        selectProductLine,
        selectCity,
        modalTitle
    },
    props: {
        editInfo: {
            type: Object,
            default() {
                return {}
            }
        },
        isShow: {
            type: Boolean,
            default: false
        },
    },

    data: function () {
        return {
            formData: this.deepCopy(orgFormData),
            loadingSave: false,
            fieldList: []
        }
    },
    created() {
        this.queryFieldList()

        if (!isEmpty(this.editInfo)) {
            this.formData = {...this.deepCopy(orgFormData), ...this.deepCopy(this.editInfo)}
            this.handleEditForm()
        } else {
            this.formData = this.deepCopy(orgFormData)
        }
    },
    mounted() {
    },
    methods: {
        fieldChange(item) {
            let field = this.fieldList.find(s => s.id === item.fieldId)
            if (field) {
                item.fieldNo = field.fieldNo
            }
            //重新排序
            this.formData.relationList.forEach((s, index) => s.sort = index + 1)
        },
        queryFieldList() {
            let param = {
                url: `offlinemetricfield/queryList`,
                method: 'GET',
            }
            this.$http(param).then((res) => {
                if (res && !isEmpty(res.data)) {
                    this.fieldList = res.data
                } else {
                    this.fieldList = []
                }
            }).catch(err => {
                console.log(err);
                this.fieldList = []
            })
        },
        addRelation() {
            this.formData.relationList.push(this.deepCopy(orgRelation))
            //重新排序
            this.formData.relationList.forEach((s, index) => s.sort = index + 1)
        },
        deleteRelation(item) {
            let index = this.formData.relationList.indexOf(item)
            this.formData.relationList.splice(index, 1)
            //重新排序
            this.formData.relationList.forEach((s, index) => s.sort = index + 1)
        },
        handleEditForm() {
            if (this.formData.productLines) {
                this.formData.productLineList = this.formData.productLines.split(',')
            }
            if (this.formData.supplierCodes) {
                this.formData.supplierCodeList = this.formData.supplierCodes.split(',')
            }
            if (this.formData.channels) {
                this.formData.channelList = this.formData.channels.split(',').map(s => parseInt(s))
            }
            if (!this.formData.cityId) {
                this.formData.cityId = ''
            }
        },
        handleSaveFormData() {
            let saveFormData = this.deepCopy(this.formData)
            saveFormData.productLines = ''
            if (!isEmpty(saveFormData.productLineList)) {
                saveFormData.productLines = saveFormData.productLineList.join(',')
            }
            saveFormData.supplierCodes = ''
            if (!isEmpty(saveFormData.supplierCodeList)) {
                saveFormData.supplierCodes = saveFormData.supplierCodeList.join(',')
            }
            saveFormData.channels = ''
            if (!isEmpty(saveFormData.channelList)) {
                saveFormData.channels = saveFormData.channelList.join(',')
            }
            if (!saveFormData.cityId) {
                saveFormData.cityId = 0
            }
            return saveFormData
        },
        submitClick() {
            this.$refs.formData.validate((valid) => {
                if (valid) {
                    this.loadingSave = true
                    let formData = this.handleSaveFormData()

                    let param = {
                        url: 'offlinemetricstrategy/save',
                        method: 'POST',
                        data: formData
                    }
                    this.$http(param).then((res) => {
                        if (res.success) {
                            this.$message.success('保存成功')
                            this.$emit('callbackclose', 'success')
                        } else {
                            this.$message.error(res.msg)
                        }
                        this.loadingSave = false

                    }).catch(err => {
                        console.log(err);
                        this.$message.error('保存失败')
                        this.loadingSave = false
                    })
                }
            });

        },
        //取消
        cancelClick() {
            this.$emit('callbackclose')
        }
    }
}
</script>

<style scoped>
</style>
