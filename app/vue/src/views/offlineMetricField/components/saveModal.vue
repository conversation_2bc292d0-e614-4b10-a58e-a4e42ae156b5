<template>
    <el-dialog class="edit_pop_div" :visible="isShow" :title="formData.id?'编辑':'新增'"
               :close-on-click-modal="false" @close="cancelClick"
               :append-to-body="true" width="50%" center>
        <el-form label-width="140px"
                 :model="formData"
                 size="small"
                 ref="formData"
                 >
            <el-form-item label="离线指标编号" prop="fieldNo"
                          :rules="[{ required: true, message: '离线指标编号不能为空' }]">
                <el-input v-model="formData.fieldNo" maxlength="64" clearable :disabled="formData.id!==''"
                          placeholder="请输入离线指标编号"></el-input>
            </el-form-item>
            <el-form-item label="离线指标名称" prop="name"
                          :rules="[{ required: true, message: '离线指标名称不能为空' }]">
                <el-input v-model="formData.name" maxlength="64" clearable
                          placeholder="请输入离线指标名称"></el-input>
            </el-form-item>
            <el-form-item label="策略说明" prop="description">
                <el-input type="textarea" rows="3"  show-word-limit v-model="formData.description"
                          maxlength="256" clearable placeholder="请输入策略说明"></el-input>
            </el-form-item>
            <el-form-item label="指标逻辑" prop="script" :rules="[{ required: true, message: '指标逻辑不能为空' }]">
                <el-input type="textarea" rows="15" cols="12" v-model="formData.script" clearable
                          placeholder="请输入指标逻辑"></el-input>
            </el-form-item>
            <el-form-item label="状态" prop="status" :rules="[{ required: true, message: '状态不能为空'}]">
                <selectStatus v-model="formData.status"></selectStatus>
            </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
       <el-button type="default" size="mini" @click="cancelClick">
            取消
        </el-button>
        <el-button type="primary" :loading="loadingSave" size="mini" @click="submitClick">
            保存
        </el-button>
    </span>
    </el-dialog>

</template>

<script>

import {isEmpty} from "@/utils/util";
import selectStatus from "@/components/selectStatus";

let orgFormData = {
    id: '',
    name: '',
    fieldNo: '',
    description: '',
    script: '',
    createBy: '',
    updateBy: '',
    status: 1
}

export default {
    components: {selectStatus},
    props: {
        editInfo: {
            type: Object,
            default() {
                return {}
            }
        },
        isShow: {
            type: Boolean,
            default: false
        },
    },

    data: function () {
        return {
            formData: this.deepCopy(orgFormData),
            loadingSave: false,
        }
    },
    created() {
        if (!isEmpty(this.editInfo)) {
            this.formData = {...this.deepCopy(orgFormData), ...this.editInfo}
        } else {
            this.formData = this.deepCopy(orgFormData)
        }
    },
    mounted() {
    },
    methods: {
        submitClick() {
            this.$refs.formData.validate((valid) => {
                if (valid) {
                    this.loadingSave = true
                    let param = {
                        url: 'offlinemetricfield/save',
                        method: 'POST',
                        data: this.formData
                    }
                    this.$http(param).then((res) => {
                        if (res.success) {
                            this.$message.success('保存成功')
                            this.$emit('callbackclose', 'success')
                        } else {
                            this.$message.error(res.msg)
                        }
                        this.loadingSave = false

                    }).catch(err => {
                        console.log(err);
                        this.$message.error('保存失败')
                        this.loadingSave = false
                    })
                }
            });

        },
        //取消
        cancelClick() {
            this.$emit('callbackclose')
        }
    }
}
</script>

<style scoped>
</style>
