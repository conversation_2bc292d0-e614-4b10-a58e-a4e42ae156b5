<template>
    <div v-cloak class="custom-content-wrapper">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline" size="mini" label-width="130px">

            <el-form-item label="用户名称">
                <el-input v-model="searchForm.name" clearable maxlength="10"></el-input>
            </el-form-item>
            <el-form-item label="手机号">
                <el-input v-model="searchForm.mobileNo" clearable>
                </el-input>
            </el-form-item>
            <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择状态">
                    <el-option label="全部" :value="''"></el-option>
                    <el-option label="无效" :value="0"></el-option>
                    <el-option label="待分发" :value="1"></el-option>
                    <el-option label="待完善" :value="2"></el-option>
                    <el-option label="待邮寄" :value="3"></el-option>
                    <el-option label="待履约" :value="4"></el-option>
                    <el-option label="已履约" :value="5"></el-option>
                </el-select>
            </el-form-item>

            <el-row type="flex" justify="center" style="margin-bottom: 10px">
                <div style="margin-left: 10px">
                    <el-button size="mini" plain icon="el-icon-refresh" @click="resetClick">重置</el-button>
                </div>
                <div style="margin-left: 10px">
                    <el-button size="mini" type="primary" plain icon="el-icon-search" @click="queryClick">查询</el-button>
                </div>
            </el-row>
        </el-form>

        <el-row>
            <div style="margin-bottom: 10px;text-align: right">
                <el-button size="mini" type="primary" plain icon="el-icon-plus" @click="addClick">新增账号</el-button>
            </div>
        </el-row>

        <tablePage ref="multipleTable" :request-param="{
                url: 'account/queryPage',
                method: 'POST',
                data: this.searchForm
            }">
            <el-table-column prop="name" align="center" label="用户名称"></el-table-column>
            <el-table-column prop="mobile" align="center" label="手机号"></el-table-column>
            <el-table-column prop="status" align="center" label="状态">
                <template slot-scope="{row}">
                    <span v-if="row.status === 0">无效</span>
                    <span v-if="row.status === 1">待分发</span>
                    <span v-if="row.status === 2">待完善</span>
                    <span v-if="row.status === 3">待邮寄</span>
                    <span v-if="row.status === 4">待履约</span>
                    <span v-if="row.status === 5">已履约</span>
                </template>
            </el-table-column>

            <el-table-column prop="updateBy" align="center" label="修改人"></el-table-column>
            <el-table-column prop="updateTime" align="center" label="更新时间">
                <template slot-scope="{row}">
                    {{row.updateTime | formatDate}}
                </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="150" fixed="right">
                <template slot-scope="{row}">
                    <el-button type="primary" size="mini" plain icon="el-icon-edit" @click="editClick(row)">编辑</el-button>
                </template>
            </el-table-column>
        </tablePage>
        <saveModal :edit-info="editInfo" :is-show="showSaveModal" v-if="showSaveModal" @callbackclose="closeSaveModal"></saveModal>

    </div>
</template>


<script>
import saveModal from './components/saveModal'
import tablePage from "@/components/tablePage";

let orgSearchForm = {
    name: '',
    mobileNo: '',
    status: '',
}


export default {
    components: {
        saveModal,
        tablePage
    },
    data() {
        return {
            total: 1,
            tableData:[],
            listQuery: {
                page: 1,
                limit: 10
            },
            searchForm: this.deepCopy(orgSearchForm),
            editInfo: {},
            showSaveModal: false
        }
    },

    created() {
    },
    mounted() {
        this.queryClick()
    },
    filters: {
        formatDate: function(value) {
            if (value == null) {
                return ''
            }
            let date = new Date(value)
            let y = date.getFullYear()
            let MM = date.getMonth() + 1
            MM = MM < 10 ? ('0' + MM) : MM
            let d = date.getDate()
            d = d < 10 ? ('0' + d) : d
            let h = date.getHours()
            h = h < 10 ? ('0' + h) : h
            let m = date.getMinutes()
            m = m < 10 ? ('0' + m) : m
            let s = date.getSeconds()
            s = s < 10 ? ('0' + s) : s
            return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s
        }
    },
    methods: {
        addClick(){
            this.editInfo = {}
            this.showSaveModal = true
        },
        //重置
        resetClick() {
            this.searchForm = this.deepCopy(orgSearchForm)
        },
        //查询
        queryClick() {
            this.$refs.multipleTable.searchData(1)
        },
        editClick(item) {
            this.editInfo = this.deepCopy(item)
            this.showSaveModal = true
        },
        closeSaveModal(result) {
            this.showSaveModal = false
            if (result) {
                setTimeout(()=>{
                    this.queryClick()
                },500)
            }
        }
    }
};
</script>

<style>
</style>
