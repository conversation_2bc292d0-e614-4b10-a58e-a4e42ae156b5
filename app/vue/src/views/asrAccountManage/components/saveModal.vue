<template>
    <el-dialog class="edit_pop_div" :visible="isShow" :title="isAdd ?'新增' :'编辑'"
               :close-on-click-modal="false" @close="cancelClick"
               :append-to-body="true" width="50%" center>
        <el-form label-width="140px"
                 :model="formData"
                 size="small"
                 ref="formData"
                 >
            <el-form-item label="手机号" prop="mobile" :rules="[{ required: true, message: '手机号不能为空' }]">
                <el-input v-model="formData.mobile" maxlength="20" :disabled="!isAdd"></el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password"
                          :rules="[{ required: true, message: '密码不能为空' }]">
                <el-input v-model="formData.password" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="用户名称" prop="name"
                          :rules="[{ required: true, message: '用户名称不能为空' }]">
                <el-input v-model="formData.name" maxlength="20"></el-input>
            </el-form-item>
          <el-form-item label="收件人姓名" prop="mailName">
            <el-input v-model="formData.mailName" maxlength="32"></el-input>
          </el-form-item>
          <el-form-item label="收件人手机号" prop="mailPhone">
            <el-input v-model="formData.mailPhone" maxlength="20"></el-input>
          </el-form-item>
            <el-form-item label="邮寄地址" prop="mailAddress">
                <el-input v-model="formData.mailAddress" maxlength="64"></el-input>
            </el-form-item>
            <el-form-item label="收款方式" prop="paymentMethod">
                <el-select v-model="formData.paymentMethod" placeholder="请选择收款方式">
                    <el-option label="支付宝" :value="1"></el-option>
                    <el-option label="银行卡" :value="2"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="账号/卡号" prop="paymentCardNo">
                <el-input v-model="formData.paymentCardNo" maxlength="64"></el-input>
            </el-form-item>
          <el-form-item label="收款人姓名" prop="paymentName" v-if="formData.paymentMethod === 2">
            <el-input v-model="formData.paymentName" maxlength="32"></el-input>
          </el-form-item>
            <el-form-item label="状态" prop="status" :rules="[{ required: true, message: '状态不能为空'}]">
                <el-select v-model="formData.status" placeholder="请选择状态">
                    <el-option label="无效" :value="0"></el-option>
                    <el-option label="待分发" :value="1"></el-option>
                    <el-option label="待完善" :value="2"></el-option>
                    <el-option label="待邮寄" :value="3"></el-option>
                    <el-option label="已邮寄" :value="4"></el-option>
                    <el-option label="已履约" :value="5"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="资料收集方式" prop="collectType">
                <el-select v-model="formData.collectType" placeholder="请选择资料收集方式">
                    <el-option label="设备采集" :value="0"></el-option>
                    <el-option label="手机自采" :value="1"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <span style="color: #ed5565">注意：设备采集 需要邮寄设备给检查员</span>
            </el-form-item>

        </el-form>

        <span slot="footer" class="dialog-footer">
       <el-button type="default" size="mini" @click="cancelClick">
            取消
        </el-button>
        <el-button type="primary" :loading="loadingSave" size="mini" @click="submitClick">
            保存
        </el-button>
    </span>
    </el-dialog>

</template>

<script>

import {isEmpty} from "@/utils/util";

let orgFormData = {
    userAccount: "",
    name: "",
    password: "",
    mobile: "",
    mailAddress: "",
    status: '',
    updateBy: "",
    updateTime: "",
    paymentMethod: 1,
    paymentCardNo: "",
    paymentName: "",
    mailName: "",
    mailPhone: "",
    collectType: 0,
}

export default {
    props: {
        editInfo: {
            type: Object,
            default() {
                return {}
            }
        },
        isShow: {
            type: Boolean,
            default: false
        },
    },

    data: function () {
        return {
            formData: this.deepCopy(orgFormData),
            loadingSave: false,
            isAdd: true
        }
    },
    created() {
        if (!isEmpty(this.editInfo)) {
            this.formData = {...this.deepCopy(orgFormData), ...this.editInfo}
            this.isAdd = false
        } else {
            this.formData = this.deepCopy(orgFormData)
            this.isAdd = true
        }
    },
    mounted() {
    },
    methods: {
        submitClick() {
            this.$refs.formData.validate((valid) => {
                if (valid) {
                    this.loadingSave = true
                    let param = {
                        url: this.isAdd ? 'account/add' :'account/update',
                        method: 'POST',
                        data: this.formData
                    }
                    this.$http(param).then((res) => {
                        if (res.success) {
                            this.$message.success('保存成功')
                            this.$emit('callbackclose', 'success')
                        } else {
                            this.$message.error(res.msg)
                        }
                        this.loadingSave = false

                    }).catch(err => {
                        console.log(err);
                        this.$message.error('保存失败')
                        this.loadingSave = false
                    })
                }
            });

        },
        //取消
        cancelClick() {
            this.$emit('callbackclose')
        }
    }
}
</script>

<style scoped>
</style>
