import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'

import '@/styles/index.scss' // global css
import '@/styles/element-reset.scss' // global css
import VideoJS from 'video.js'// video js
import 'video.js/dist/video-js.css' // video js

import App from './App'
import router from './router'

import http from './utils/http'
import vueTools from './utils/vuetools'
import mixin from './utils/mixin'
import VueAMap from 'vue-amap'


    /**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
// if (process.env.NODE_ENV === 'production') {
//   const { mockXHR } = require('../mock')
//   mockXHR()
// }
// 事件总线
Vue.prototype.$bus = new Vue()

Vue.use(Element, {
    size: Cookies.get('size') || 'medium' // set element-ui default size
})
Vue.prototype.$videoJS = VideoJS;
Vue.use(http)
Vue.use(vueTools)
Vue.mixin(mixin)
Vue.use(VueAMap)

VueAMap.initAMapApiLoader({
    key: 'c154ec6cc866a658e5716c3ea86f5ec1',
    plugin: ['AMap.Geolocation','AMap.Autocomplete', 'AMap.PlaceSearch', 'AMap.OverView', 'AMap.Scale', 'AMap.OverView', 'AMap.ToolBar', 'AMap.MapType', 'AMap.PolyEditor', 'AMap.CircleEditor'],
    v: '1.4.4',
    securityJsCode:'257071383c65a71f2180a0d6f0931ab7'
});

Vue.config.productionTip = false
new Vue({
    el: '#app',
    router,
    render: h => h(App)
})
/** 连接预发 qa 可打开 */
// if (process.env.NODE_ENV === 'development') {
//     let cookieStr =
//         '\n' +
//         'JSESSIONID=0A0A0B986FDC2F8AB57AC54917394861; state=72fb0156241f4e7c81882affb33be6b6; access_token=1ecd8a99b6c7551e55af8626f0f4dfb0; toggletopic=2; __tctmc=102596417.93733272; __tctmd=102596417.128401801; __tctmu=102596417.0.0; __tctmz=102596417.1691373067069.2.1.utmccn=(direct)|utmcsr=(direct)|utmcmd=(none); longKey=1691114280393328; __tctrack=0; __tctma=102596417.1691114280393328.1691114280045.1691373067069.1691562020646.3; TCSession_N100933=s9NrENHcCuXxlnwH3NyT497K58cIAW94uRs47c2UOGRXkDpHp3reGebnRFc518YD; TCSession_N3302351=hElHo2w2ItF4gV043jA0RzfrjURCfnrF7PFKpPXivaRKoyACTLrnr0Dz2pG1Fk0i; sidebarStatus=0'
//
//     cookieStr.split(';').forEach((el) => {
//         let arr = el.split('=')
//         Cookies.set(arr[0].trim(), arr[1].trim())
//     })
// }
