import axios from 'axios'
axios.defaults.withCredentials = true

const http = {
    install(Vue) {
        Vue.prototype.$http = config => {
            const promise = new Promise((resolve, reject) => {
                if (!config.data) {
                    config.data = {}
                }
                config.url = '/carAsrAdmin/' + config.url

                if (/get/i.test(config.method) && config.data) {
                    config.params = {
                        ...config.data
                    }
                }

                axios(config).then(result => {
                    if (result.data) {
                        resolve(result.data)
                    } else {
                        reject()
                    }
                }).catch(e => {
                    console.warn(e)
                    reject(e)
                })
            })

            return promise
        }
    }
}

export default http
