import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/* Router Modules */

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
    {
        path: '/',
        component: Layout,
        children: [

            {
                path: 'offlineMetricField',
                component: () => import('@/views/offlineMetricField/index'),
                name: 'offlineMetricField',
                meta: {title: '离线指标', noCache: false },
            },
            {
                path: 'offlineMetricStrategy',
                component: () => import('@/views/offlineMetricStrategy/index'),
                name: 'offlineMetricField',
                meta: {title: '离线策略', noCache: false },
            },
            {
                path: 'asrAccountManage',
                component: () => import('@/views/asrAccountManage/index'),
                name: 'asrAccountManage',
                meta: {title: '账号管理', noCache: false },
            },
            {
                path: 'asrSoundManage',
                component: () => import('@/views/asrSoundManage/index'),
                name: 'asrSoundManage',
                meta: {title: '录音管理', noCache: false },
            }
        ]
    },
]



const createRouter = () => new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: [...constantRoutes]
})

const router = createRouter()

export function resetRouter() {
    const newRouter = createRouter()
    router.matcher = newRouter.matcher // reset router
}

router.beforeEach((to, from, next) => {
    if (to){
        window.currentPageRouter = to.path
    }
    next()
})

export default router
