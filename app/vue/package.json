{"name": "tc-bus-resource-std-admin-vue", "version": "1.0.0", "scripts": {"dev": "vue-cli-service serve", "lint": "eslint --ext .js,.vue src", "build": "vue-cli-service build", "preview": "node build/index.js --preview", "new": "plop", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@antv/l7": "^2.15.4", "@antv/l7plot": "^0.5.2", "axios": "0.18.1", "clipboard": "2.0.4", "codemirror": "5.45.0", "core-js": "3.6.5", "dayjs": "^1.11.8", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "^4.2.1", "el-tree-transfer": "^2.4.7", "element-ui": "2.15.13", "file-saver": "2.0.1", "fuse.js": "3.4.4", "js-cookie": "2.2.0", "jsonlint": "1.6.3", "jszip": "3.2.1", "less": "^3.11.1", "less-loader": "^5.0.0", "manba": "^1.3.5", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "screenfull": "4.2.0", "script-loader": "0.7.2", "sortablejs": "1.8.4", "video.js": "^8.23.4", "vue": "2.6.10", "vue-amap": "^0.5.10", "vue-count-to": "1.0.13", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuedraggable": "2.20.0", "vuex": "3.1.0", "xlsx": "0.14.1"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/devtools-api": "^8.0.1", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "runjs": "4.3.2", "sass": "1.26.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "license": "MIT", "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}}